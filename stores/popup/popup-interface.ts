import { ButtonProps } from "@/components/ui/button/button-types";
import { ModalSizes } from "flowbite-react";
import { DynamicStringEnumKeysOf } from "flowbite-react/types";

export type PopupAction = {
  label: string;
} & Partial<ButtonProps>;

export type PopupType = "info" | "warning" | "error" | "success";

export interface PopupState {
  isOpen: boolean;
  withHeader?: boolean;
  size?: DynamicStringEnumKeysOf<ModalSizes>;
  type?: PopupType;
  title: string;
  message: string;
  icon?: React.ReactNode;
  classNameContainerActions?: string;
  actions: PopupAction[];
  className?: string;
  additionalContent?: React.ReactNode;
  openPopup: (config: {
    withHeader?: boolean;
    title: string;
    type?: PopupType;
    size?: DynamicStringEnumKeysOf<ModalSizes>;
    message: string;
    icon?: React.ReactNode;
    actions?: PopupAction[];
    className?: string;
    classNameContainerActions?: string;
    additionalContent?: React.ReactNode;
  }) => void;
  closePopup: () => void;
}
