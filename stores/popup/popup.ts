import { create } from "zustand";
import { PopupState } from "./popup-interface";

export const usePopupStore = create<PopupState>((set) => ({
  isOpen: false,
  withHeader: true,
  size: "md",
  type: undefined,
  title: "",
  message: "",
  icon: undefined,
  classNameContainerActions: "",
  additionalContent: undefined, // Initialize additionalContent as undefined
  actions: [],
  openPopup: ({
    title,
    type,
    message,
    icon,
    withHeader = true,
    size = "md",
    actions,
    classNameContainerActions,
    additionalContent,
  }) =>
    set({
      isOpen: true,
      title,
      type,
      message,
      icon,
      withHeader,
      size,
      classNameContainerActions,
      actions: actions || [],
      additionalContent,
    }),
  closePopup: () =>
    set({
      isOpen: false,
      withHeader: true,
      size: "md",
      title: "",
      message: "",
      icon: undefined,
      classNameContainerActions: "",
      actions: [],
      additionalContent: undefined, // Reset additionalContent when closing the popup
    }),
}));
