import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
import { routes } from "./lib/routes";
import { ROLE_ENUM } from "./constants";
import { isPathMatchingPattern } from "./utils/helpers";

const SECURITY_HEADERS = {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
  "Strict-Transport-Security": "max-age=63072000; includeSubDomains; preload",
};

const publicPaths = ["/auth/login", "/auth/forgot-password"];
const inputPaths = [routes.managements.form.create];
const editPaths = [routes.managements.form.edit("{id}")];
const intelAccessPaths = ["", routes.managements.form.view("{id}")];
const notIntelAccessPaths = [
  routes.managements.form.index,
  routes.managements.form.view("{id}"),
  ...inputPaths,
  ...editPaths,
];

const accessRolePaths: Record<ROLE_ENUM, string[]> = {
  [ROLE_ENUM.INTEL]: intelAccessPaths,
  [ROLE_ENUM.PIDUM]: notIntelAccessPaths,
  [ROLE_ENUM.PIDSUS]: notIntelAccessPaths,
  [ROLE_ENUM.KEJARI]: notIntelAccessPaths,
  [ROLE_ENUM.KEJATI]: notIntelAccessPaths,
  [ROLE_ENUM.KEJAGUNG]: notIntelAccessPaths,
  [ROLE_ENUM.ADMIN]: [],
};

export async function middleware(request: NextRequest) {
  const { pathname, origin } = request.nextUrl;
  const token = await getToken({ req: request });
  const pathSegments = pathname.split("/").filter(Boolean);
  const firstSegment = pathSegments[0];

  if (token) {
    const role = token.user?.role as ROLE_ENUM;
    const userAccess = token.user?.user_access;

    if (
      (pathname === routes.dashboard || firstSegment !== role) &&
      !publicPaths.includes(pathname)
    ) {
      const targetPath = pathname === "/" ? `/${role}` : `/${role}${pathname}`;
      return NextResponse.redirect(new URL(targetPath, origin));
    }

    if (firstSegment && publicPaths.includes(pathname)) {
      return NextResponse.redirect(new URL(`/${role}`, origin));
    }

    if (
      firstSegment &&
      firstSegment !== role &&
      !publicPaths.includes(pathname)
    ) {
      return new NextResponse("Not Found", { status: 404 });
    }

    if (
      inputPaths.some((path) =>
        isPathMatchingPattern(`/${role}/${path}`, pathname),
      ) &&
      !userAccess.can_input
    ) {
      return new NextResponse(
        `<!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>403 Forbidden</title>
          <style>
            body {
              font-family: system-ui, -apple-system, sans-serif;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100vh;
              margin: 0;
              background-color: #f5f5f5;
            }
            .container {
              text-align: center;
              padding: 2rem;
              background: white;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            h1 { 
              color: #dc2626;
              font-size: 2.5rem;
              margin-bottom: 1rem;
            }
            p {
              color: #4b5563;
              font-size: 1.1rem;
              margin-bottom: 1.5rem;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>403 Forbidden</h1>
            <p>Maaf, Anda tidak memiliki akses untuk membuat data baru</p>
          </div>
        </body>
        </html>`,
        { status: 403, headers: { "Content-Type": "text/html" } },
      );
    }

    if (
      editPaths.some((path) =>
        isPathMatchingPattern(`/${role}/${path}`, pathname),
      ) &&
      !userAccess.can_edit
    ) {
      console.error("User does not have permission to edit");
      return new NextResponse(
        `<!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>403 Forbidden</title>
          <style>
            body {
              font-family: system-ui, -apple-system, sans-serif;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100vh;
              margin: 0;
              background-color: #f5f5f5;
            }
            .container {
              text-align: center;
              padding: 2rem;
              background: white;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            h1 { 
              color: #dc2626;
              font-size: 2.5rem;
              margin-bottom: 1rem;
            }
            p {
              color: #4b5563;
              font-size: 1.1rem;
              margin-bottom: 1.5rem;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>403 Forbidden</h1>
            <p>Maaf, Anda tidak memiliki akses untuk mengubah data</p>
          </div>
        </body>
        </html>`,
        { status: 403, headers: { "Content-Type": "text/html" } },
      );
    }

    if (
      accessRolePaths?.[role].some((path) =>
        isPathMatchingPattern(`/${role}/${path}`, pathname),
      ) &&
      role !== ROLE_ENUM.ADMIN
    ) {
      return NextResponse.next();
    } else {
      if (role !== ROLE_ENUM.INTEL && role !== ROLE_ENUM.ADMIN) {
        return NextResponse.redirect(
          new URL(`${role}${routes.managements.form.index}`, origin),
        );
      } else if (role === ROLE_ENUM.INTEL) {
        return NextResponse.redirect(
          new URL(`${role}${routes.dashboard}`, origin),
        );
      }
    }
  } else {
    if (publicPaths.includes(pathname)) {
      return NextResponse.next();
    }

    const loginUrl = new URL(routes.login, origin);
    loginUrl.searchParams.set("callbackUrl", pathname);
    return NextResponse.redirect(loginUrl);
  }

  const response = NextResponse.next();
  Object.entries(SECURITY_HEADERS).forEach(([header, value]) => {
    response.headers.set(header, value);
  });

  return response;
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico|assets|images).*)"],
};
