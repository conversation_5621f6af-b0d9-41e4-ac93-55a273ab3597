import apiClient from "@/lib/axios/axios";
import {
  GetListFormParams,
  GetListFormResponse,
  GetListRequestEditFormResponse,
  SuspectDetailData,
  SuspectEditDetailData,
} from "./form-get.interface";
// import { STATUS_PERKARA_ENUM } from "@/constants";

export const getListForm = async (params: GetListFormParams) => {
  const response = await apiClient.get<GetListFormResponse>(
    `/suspects/${params.unitId}`,
    {
      params: params.query,
      // mockData: {
      //   status_code: 200,
      //   message: "Form List fetched successfully",
      //   data: {
      //     total_data: 1,
      //     total_page: 1,
      //     current_page: 1,
      //     limit: 10,
      //     data: [
      //       {
      //         id: 6,
      //         name: "<PERSON><PERSON>",
      //         job: "dokter",
      //         umur: 27,
      //         alamat: "",
      //         pasal:
      //           "Tersangka diduga melanggar Pasal 372, 351(2) <PERSON><PERSON>HP, Pasal 59(1) UU Psikotropika, serta <PERSON> 64 <PERSON><PERSON><PERSON> karena pengg<PERSON>pan, pen<PERSON><PERSON><PERSON><PERSON> berat, dan edar psiko<PERSON>pika tanpa izin secara berlanjut.",
      //         status_perkara: "DPO",
      //         jenis_perkara: "Pidana Umum",
      //         tanggal: "18 Juni 2025",
      //         catatan:
      //           "Tersangka diduga melakukan penggelapan dan penganiayaan berat terhadap korban.",
      //       },
      //       {
      //         id: 7,
      //         name: "Budi Santoso",
      //         job: "pengacara",
      //         umur: 35,
      //         alamat: "Jl. Merdeka No. 10, Jakarta",
      //         pasal:
      //           "Tersangka diduga melanggar Pasal 378 KUHP karena penipuan.",
      //         status_perkara: "Dalam Proses",
      //         jenis_perkara: "Pidana Umum",
      //         tanggal: "20 Juni 2025",
      //         catatan:
      //           "Tersangka diduga melakukan penipuan terhadap korban dengan modus investasi bodong.",
      //       },
      //     ],
      //   },
      //   error: false,
      //   detail: {
      //     request_id: "4c20112c-3fdf-4af5-ba46-930c9f31fce4",
      //     query: "",
      //     path: "/api/v1/form/list",
      //     method: "POST",
      //     response_time: "33.352542ms",
      //     request_at: "2025-06-26T02:37:33+07:00",
      //     errors: null,
      //   },
      // },
    },
  );

  return response?.data;
};

export const getListRequestEditForm = async (params: GetListFormParams) => {
  const response = await apiClient.get<GetListRequestEditFormResponse>(
    `/suspect-cases/request-form/list/${params.unitId}`,
    {
      params: params.query,
      // mockData: {
      //   status_code: 200,
      //   message: "Request Edit Form List fetched successfully",
      //   data: {
      //     total_data: 1,
      //     total_page: 1,
      //     current_page: 1,
      //     limit: 10,
      //     data: [
      //       {
      //         id: 1,
      //         name: "John Doe",
      //         job: "Pengacara",
      //         umur: 30,
      //         alamat: "Jl. Merdeka No. 1, Jakarta",
      //         pasal: "Pasal 378 KUHP",
      //         status_perkara: STATUS_PERKARA_ENUM.DPO,
      //         jenis_perkara: "Pidana Umum",
      //         tanggal: "2025-06-01",
      //         catatan: "Catatan penting mengenai kasus ini.",
      //         asal_perkara: "Asal Perkara 1",
      //         status_request: "Pending",
      //         suspect_id: 1,
      //         kejaksaan_unit_id: unitId,
      //       },
      //     ],
      //   },
      // },
    },
  );
  return response?.data;
};

export const getSuspectDetail = async (
  id: number,
): Promise<SuspectDetailData | null> => {
  const response = await apiClient.get<SuspectDetailData>(
    `/suspect-detail/${id}`,
    {
      showLoader: true,
    },
  );

  return response?.data;
};

export const getSuspectEditDetail = async (
  id: number,
): Promise<SuspectEditDetailData | null> => {
  const response = await apiClient.get<SuspectEditDetailData>(
    `/suspect-cases/${id}/edit`,
  );

  return response?.data;
};
