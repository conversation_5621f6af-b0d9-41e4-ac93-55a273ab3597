import { DataCreateForm } from "@/features/managements/kartu-tik/form/form-kartu-tik-interface";
import apiClient from "@/lib/axios/axios";
import {
  CheckLetterCodeResponse,
  CheckRefNumberResponse,
  RequestFormApproveReject,
  RequestFormSuspectData,
} from "./form-post.interface";

export const createSuspect = async (data: DataCreateForm) => {
  const response = await apiClient.post(`/create-suspect`, { data });

  return response?.data;
};
export const updateSuspect = async (id: number, data: DataCreateForm) => {
  const response = await apiClient.put(`/update-suspect/${id}`, { data });

  return response?.data;
};

export const requestFormSuspect = async (data: RequestFormSuspectData) => {
  const response = await apiClient.post(`/suspect-cases/request-form`, {
    data,
  });

  return response?.data;
};

export const requestFormApproveReject = async (
  data: RequestFormApproveReject,
) => {
  const response = await apiClient.post(
    `/suspect-cases/request-form/approve-or-reject`,
    {
      data,
    },
  );

  return response?.data;
};

export const checkLetterCode = async (
  data: Pick<DataCreateForm, "letter_code">,
) => {
  const response = await apiClient.post<CheckLetterCodeResponse>(
    `/suspect-cases/check-lettercode`,
    {
      data,
    },
  );

  return response?.data;
};

export const checkRefNumber = async (
  data: Pick<DataCreateForm, "ref_number">,
) => {
  const response = await apiClient.post<CheckRefNumberResponse>(
    `/suspect-cases/check-refnumber`,
    {
      data,
    },
  );

  return response?.data;
};
