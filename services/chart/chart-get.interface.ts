export interface GetChartDataParams {
  unitId: number;
}
export interface GetChartDataResponse {
  pidum: PidumChartData;
  pidsus: PidsusChartData;
}
export interface PidumChartData {
  total: 0;
  total_perstatus: {
    total_tersangka: 0;
    total_terpidana: 0;
    total_terdakwa: 0;
    total_dpo: 0;
  };
  case_origin_percentage: {
    bareskrim: 0;
    polri: 0;
    polda: 0;
    polres: 0;
    polsek: 0;
    ppns: 0;
    kpk: 0;
    kejaksaan: 0;
  };
}
export interface PidsusChartData {
  total: 0;
  total_perstatus: {
    total_tersangka: 0;
    total_terpidana: 0;
    total_terdakwa: 0;
    total_dpo: 0;
  };
  case_origin_percentage: {
    bareskrim: 0;
    polri: 0;
    polda: 0;
    polres: 0;
    polsek: 0;
    ppns: 0;
    kpk: 0;
    kejaksaan: 0;
  };
}
