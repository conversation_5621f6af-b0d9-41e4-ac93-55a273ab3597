"use client";

import { createElement, useCallback, useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { usePopupStore } from "@/stores/popup/popup";
import { useIdleTimeout } from "./use-idle-timeout";
import { HiOutlineClock } from "react-icons/hi";
import { signOutToken } from "./use-auth";
import { useSession } from "next-auth/react";

interface UseIdleConfirmationOptions {
  idleTimeout?: number;
  warningTimeout?: number;
  onTimeout?: () => void;
  title?: string;
  message?: string;
}

export const useIdleConfirmation = ({
  idleTimeout = 1 * 60 * 1000, // 1 minute of inactivity example
  warningTimeout = 60 * 1000, // 1 minute warning before logout
  onTimeout,
  title = "Ups! Sesi Kamu Berakhir",
  message = "Kami tidak mendeteksi aktivitas dalam 30 menit terakhir. Demi keamanan data, silakan login kembali, ya!.",
}: UseIdleConfirmationOptions = {}) => {
  const pathname = usePathname();
  const { data: dataSession } = useSession();
  const { openPopup, closePopup, isOpen } = usePopupStore();
  const [warningTimer, setWarningTimer] = useState<NodeJS.Timeout | null>(null);
  const [countdownTimer, setCountdownTimer] = useState<NodeJS.Timeout | null>(
    null,
  );

  // Check if current path is a form path
  const isFormPath = useCallback(() => {
    return (
      pathname?.includes("/management/form/create") ||
      pathname?.includes("/management/form/edit")
    );
  }, [pathname]);

  const handleContinueSession = useCallback(() => {
    closePopup();
    if (warningTimer) {
      clearTimeout(warningTimer);
      setWarningTimer(null);
    }
    if (countdownTimer) {
      clearTimeout(countdownTimer);
      setCountdownTimer(null);
    }
  }, [closePopup, warningTimer, countdownTimer]);

  const handleTimeoutInternal = useCallback(() => {
    closePopup();
    if (warningTimer) {
      clearTimeout(warningTimer);
      setWarningTimer(null);
    }
    if (countdownTimer) {
      clearTimeout(countdownTimer);
      setCountdownTimer(null);
    }
    if (onTimeout) {
      onTimeout();
    }
  }, [closePopup, onTimeout, warningTimer, countdownTimer]);

  const showFormAlert = useCallback(
    (currentCountdown: number) => {
      const saveLabel =
        currentCountdown > 0
          ? `Save changes (${currentCountdown}s)`
          : "Save changes";

      openPopup({
        title: "Unsaved Changes",
        message:
          "You have unsaved changes. Do you want to save them before leaving?",
        type: "warning",
        icon: createElement(
          "div",
          null,
          createElement(
            "div",
            {
              className:
                "flex h-12 w-12 items-center justify-center rounded-full border-8 border-yellow-100 bg-yellow-200",
            },
            createElement(HiOutlineClock, {
              className: "h-6 w-6 text-yellow-600",
            }),
          ),
        ),
        size: "xl",
        withHeader: false,
        actions: [
          {
            label: "Discard",
            onClick: handleContinueSession,
            variant: "primary" as const,
          },
          {
            label: saveLabel,
            onClick: handleTimeoutInternal,
            variant: "outline" as const,
          },
        ],
        classNameContainerActions: "justify-end",
      });
    },
    [handleContinueSession, handleTimeoutInternal, openPopup],
  );

  const showLogoutAlert = useCallback(async () => {
    console.log("showLogoutAlert called");
    await signOutToken(dataSession?.access_token?.token ?? "");
    openPopup({
      title,
      message,
      type: "warning",
      icon: createElement(
        "div",
        null,
        createElement(
          "div",
          {
            className:
              "flex h-12 w-12 items-center justify-center rounded-full border-8 border-yellow-100 bg-yellow-200",
          },
          createElement(HiOutlineClock, {
            className: "h-6 w-6 text-yellow-600",
          }),
        ),
      ),
      size: "xl",
      withHeader: false,
      actions: [
        {
          label: "Login Kembali",
          onClick: handleTimeoutInternal,
          variant: "outline" as const,
          className: "w-50",
        },
      ],
    });
  }, [title, message, handleTimeoutInternal, openPopup, dataSession]);

  const showIdleWarning = useCallback(() => {
    if (isOpen) return;

    if (isFormPath()) {
      showFormAlert(0);

      const timer = setTimeout(() => {
        handleTimeoutInternal();
      }, warningTimeout);

      setWarningTimer(timer);

      const countdownStartTime = warningTimeout - 10000;

      if (countdownStartTime > 0) {
        const countdownStartTimer = setTimeout(() => {
          let secondsLeft = 10;
          showFormAlert(secondsLeft);

          const countdownInterval = setInterval(() => {
            secondsLeft -= 1;
            showFormAlert(secondsLeft);

            if (secondsLeft <= 0) {
              clearInterval(countdownInterval);
            }
          }, 1000);

          setCountdownTimer(countdownInterval);
        }, countdownStartTime);

        setTimeout(() => {
          clearTimeout(countdownStartTimer);
        }, countdownStartTime + 100);
      } else {
        let secondsLeft = Math.floor(warningTimeout / 1000);
        showFormAlert(secondsLeft);

        const countdownInterval = setInterval(() => {
          secondsLeft -= 1;
          showFormAlert(secondsLeft);

          if (secondsLeft <= 0) {
            clearInterval(countdownInterval);
          }
        }, 1000);

        setCountdownTimer(countdownInterval);
      }
    } else {
      showLogoutAlert();

      const timer = setTimeout(() => {
        handleTimeoutInternal();
      }, warningTimeout);

      setWarningTimer(timer);
    }
  }, [
    isOpen,
    isFormPath,
    showFormAlert,
    showLogoutAlert,
    handleTimeoutInternal,
    warningTimeout,
  ]);

  const { isIdle, pauseTimer, resumeTimer } = useIdleTimeout({
    timeout: idleTimeout,
    onIdle: showIdleWarning,
    onActive: () => {
      if (warningTimer) {
        clearTimeout(warningTimer);
        setWarningTimer(null);
      }
      if (countdownTimer) {
        clearTimeout(countdownTimer);
        setCountdownTimer(null);
      }
    },
  });

  useEffect(() => {
    return () => {
      if (warningTimer) {
        clearTimeout(warningTimer);
      }
      if (countdownTimer) {
        clearTimeout(countdownTimer);
      }
    };
  }, [warningTimer, countdownTimer]);

  return {
    isIdle,
    pauseTimer,
    resumeTimer,
    isWarningShown: isOpen,
  };
};
