import { useRouter } from "next/navigation";
import { STATUS_APPROVED_ENUM } from "@/constants";
import { RequestFormSuspect } from "@/features/home/<USER>/table-section/request-form-suspect";
import { usePopupStore } from "@/stores/popup/popup";
import { routes } from "../routes";
import { Icon } from "@/components/shared/icon";

export const useHandleRequestEdit = () => {
  const router = useRouter();
  const { openPopup, closePopup } = usePopupStore();

  const handleEdit = (id: number, status: STATUS_APPROVED_ENUM) => {
    const openRejectPopup = () => {
      openPopup({
        title: "<PERSON>ga<PERSON><PERSON>",
        message: "Pengajuan perubahan data telah ditolak.",
        type: "error",
        icon: <Icon type="error" />,
        size: "xl",
        withHeader: true,
        classNameContainerActions: "justify-end",
        actions: [
          {
            label: "Tutup",
            type: "button",
            variant: "outline",
            onClick: () => closePopup(),
          },
          {
            label: "Ajukan <PERSON>",
            type: "button",
            onClick: () => {
              closePopup();
              openPopup({
                title: "Ajukan Perubahan Data",
                message: "Masukkan alasan perubahan data",
                additionalContent: <RequestFormSuspect id={id} />,
                type: "warning",
                icon: <Icon type="info" />,
                size: "xl",
                withHeader: false,
              });
            },
          },
        ],
      });
    };

    const openPendingPopup = () => {
      openPopup({
        title: "Pengajuan Sedang Diproses",
        message: "Pengajuan perubahan data masih dalam proses.",
        type: "info",
        icon: <Icon type="info" />,
        size: "xl",
        withHeader: true,
        classNameContainerActions: "justify-end",
        actions: [
          {
            label: "Tutup",
            type: "button",
            variant: "outline",
            onClick: () => closePopup(),
          },
        ],
      });
    };

    const openDefaultPopup = () => {
      openPopup({
        title: "Konfirmasi Pengajuan Perubahan Data",
        message: "Masukkan alasan perubahan data",
        icon: <Icon type="info" />,
        additionalContent: <RequestFormSuspect id={id} />,
        size: "xl",
        withHeader: false,
        classNameContainerActions: "justify-end",
      });
    };

    switch (status) {
      case STATUS_APPROVED_ENUM.APPROVED:
        router.push(routes.managements.form.edit(id));
        break;
      case STATUS_APPROVED_ENUM.REJECTED:
        openRejectPopup();
        break;
      case STATUS_APPROVED_ENUM.PENDING:
        openPendingPopup();
        break;
      default:
        openDefaultPopup();
        break;
    }
  };

  return { handleEdit };
};
