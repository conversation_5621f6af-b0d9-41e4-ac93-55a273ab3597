import { Icon } from "@/components/shared/icon";
import { requestFormApproveReject } from "@/services/form/form-post";
import { RequestFormApproveReject } from "@/services/form/form-post.interface";
import { useLoadingOverlayStore } from "@/stores/loading-overlay/loading-overlay";
import { usePopupStore } from "@/stores/popup/popup";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { ErrorResponse } from "../error-handler/error-handler.interface";

interface UseApproveRejectRequestForm {
  onSuccessCallback?: () => void;
  onErrorCallback?: (error: ErrorResponse) => void;
  onSubmitCallback?: () => void;
}

export const useApproveRejectRequestForm = ({
  onSuccessCallback,
  onErrorCallback,
  onSubmitCallback,
}: UseApproveRejectRequestForm = {}) => {
  const { openPopup, closePopup } = usePopupStore();
  const { hide, show } = useLoadingOverlayStore();
  const queryClient = useQueryClient();

  const submitApproveRejectRequestForm = useMutation({
    mutationFn: (data: RequestFormApproveReject) =>
      requestFormApproveReject(data),
    onSuccess: () => {
      hide();
      queryClient.invalidateQueries({ queryKey: ["listNotification"] });
      queryClient.invalidateQueries({ queryKey: ["listRequestEditForm"] });
      openPopup({
        title: "Berhasil Menyimpan Data",
        message: "Data berhasil disimpan",
        classNameContainerActions: "justify-end",
        icon: <Icon type="success" />,
        withHeader: false,
        size: "xl",
        actions: [
          {
            label: "Tutup",
            type: "button",
            onClick: () => {
              closePopup();
            },
          },
        ],
      });
      onSuccessCallback?.();
    },
    onError: (error: ErrorResponse) => {
      hide();
      onErrorCallback?.(error);
      openPopup({
        title: "Gagal Menyimpan Data",
        message: `Terjadi kesalahan saat menyimpan data, silahkan coba lagi. ${error.message}`,
        classNameContainerActions: "justify-end",
        icon: <Icon type="error" />,
        size: "xl",
        actions: [
          {
            label: "Kembali",
            type: "button",
            onClick: () => closePopup(),
          },
        ],
      });
    },
  });

  const handleApproveReject = (data: RequestFormApproveReject) => {
    show("Memproses permintaan...");
    submitApproveRejectRequestForm.mutateAsync(data);
    onSubmitCallback?.();
  };

  return {
    handleApproveReject,
  };
};
