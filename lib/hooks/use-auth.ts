import axios from "axios";

export const signOutToken = async (accessToken: string) => {
  try {
    if (accessToken) {
      return await axios.put(
        `${process.env.NEXT_PUBLIC_API_URL_V1}/auth/logout`,
        null,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );
    }
  } catch (error) {
    return Promise.reject(new Error("Failed to log out on backend: " + error));
  }
};
