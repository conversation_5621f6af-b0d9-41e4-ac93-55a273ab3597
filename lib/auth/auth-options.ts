/* eslint-disable @typescript-eslint/no-explicit-any */
import type { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";
import { z } from "zod";
import { signOutToken } from "../hooks/use-auth";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL_V1 || "http://localhost:8000/api/v1";

const MAX_LOGIN_ATTEMPTS = 5;
const RATE_LIMIT_WINDOW_MS = 15 * 60 * 1000;
const failedAttempts = new Map<
  string,
  { attempts: number; lastAttempt: number }
>();

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        try {
          const schema = z.object({
            email: z.string().email(),
            password: z.string(),
          });

          const validatedCredentials = schema.parse(credentials);

          const now = Date.now();
          const attempt = failedAttempts.get(validatedCredentials.email) || {
            attempts: 0,
            lastAttempt: 0,
          };

          if (now - attempt.lastAttempt < RATE_LIMIT_WINDOW_MS) {
            if (attempt.attempts >= MAX_LOGIN_ATTEMPTS) {
              throw new Error(
                "Too many login attempts. Please try again later.",
              );
            }
          }

          const response = await axios.post(`${API_BASE_URL}/auth/login`, {
            email: validatedCredentials.email,
            password: validatedCredentials.password,
          });

          if (!response.data || !response.data.data) {
            failedAttempts.set(validatedCredentials.email, {
              attempts: attempt.attempts + 1,
              lastAttempt: now,
            });
            throw new Error("Invalid credentials");
          }

          const user = response.data.data;

          failedAttempts.delete(validatedCredentials.email);

          return user;
        } catch (error) {
          if (axios.isAxiosError(error)) {
            const message =
              error.response?.data?.message || "Authentication failed";
            throw new Error(message);
          }
          throw new Error("Authentication failed");
        }
      },
    }),
  ],
  events: {
    async signOut({ token }) {
      const accessToken = (token as any)?.user?.access_token?.token;
      try {
        await signOutToken(accessToken);
      } catch (error) {
        console.error("Failed to sign out from backend:", error);
      }
    },
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 hours in seconds
    updateAge: 30 * 60, // 30 minutes in seconds
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async jwt({ trigger, token, user }) {
      if (user && trigger === "signIn") {
        token.user = user;
      }
      return token;
    },
    async session({ session, token }) {
      const user = token?.user as any;

      if (user) {
        session.access_token = user.access_token;
        session.user = {
          full_name: user.full_name,
          email: user.email,
          user_id: user.id,
          role: user.role,
          kejaksaan_unit: user.kejaksaan_unit,
          user_access: user.user_access,
        };

        delete (session as any).access_token.refresh_token;
      }

      return session;
    },
  },
};
