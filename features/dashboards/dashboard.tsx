"use client";
import { ReactSelect } from "@/components/ui/react-select/react-select";
import { ChartPidanaUmum } from "./chart-pidana-umum";
import { ChartPidanaKhusus } from "./chart-pidana-khusus";
import { PageHeader } from "@/components/ui/page-header/page-header";
import { UserData } from "@/types/auth";
import { useRef, useState } from "react";
import { getListKejaksaanNegeri } from "@/services/kejaksaan-negeri/kejaksaaan-negeri-get";
import { useQuery } from "@tanstack/react-query";
import { getListKejaksaanTinggi } from "@/services/kejaksaan-tinggi/kejaksaan-tinggi-get";
import { getDropdownConfig, Unit } from "./config/dropdown.config";

import { TableSectionIntel } from "./table-section-intel";
import { getChartData } from "@/services/chart/chart-get";
import { ROLE_ENUM } from "@/constants";

export interface DashboardProps {
  userData?: UserData;
}

export const Dashboard = ({ userData }: DashboardProps) => {
  const [tableSection, setTableSection] = useState<boolean>(false);
  const [enableDropdown, setEnableDropdown] = useState<boolean>(false);
  const [kejaksaanTinggiID, setKejaksaanTinggiID] = useState<number>();
  const [kejaksaanUnitID, setKejaksaanUnitID] = useState<number>();
  const tableRef = useRef<HTMLDivElement>(null);

  const role = (userData?.kejaksaan_unit.unit as Unit) || ROLE_ENUM.KEJAGUNG;

  const parentID = kejaksaanTinggiID
    ? kejaksaanTinggiID
    : userData?.kejaksaan_unit.id;
  const unitId =
    kejaksaanUnitID ?? kejaksaanTinggiID ?? userData?.kejaksaan_unit.id ?? 0;

  const { data: kejaksaanNegeriList } = useQuery({
    queryKey: ["kejaksaanNegeriList", parentID],
    queryFn: () => getListKejaksaanNegeri(parentID || 0),
    enabled: !!kejaksaanTinggiID || role === ROLE_ENUM.KEJATI, // Only run query if kejaksaanTinggiID exists and role is kejati
  });

  const { data: kejaksaanTinggiList } = useQuery({
    queryKey: ["kejaksaanTinggiList"],
    queryFn: () =>
      getListKejaksaanTinggi({
        type: ROLE_ENUM.KEJATI,
      }),
  });

  const { data: chartData, isLoading: isLoadingChartData } = useQuery({
    queryKey: ["chartData", unitId],
    queryFn: () => {
      return getChartData({
        unitId,
      });
    },
  });

  const dropdownConfig = getDropdownConfig({
    kejaksaanTinggiList: kejaksaanTinggiList?.data,
    kejaksaanNegeriList: kejaksaanNegeriList,
    kejaksaanName: userData?.kejaksaan_unit.name || "Kejaksaan",
    setKejaksaanTinggiID: setKejaksaanTinggiID,
    setKejaksaanUnitID: setKejaksaanUnitID,
    enableDropdown,
  });

  const selectFilter = (dropdownConfig[role] || []).map((item) => ({
    element: (
      <ReactSelect
        isClearable
        options={item.options}
        placeholder={item.placeholder}
        isDisabled={item.disabled}
        onChange={(e) => {
          item.onClick?.(e?.value ? Number(e.value) : undefined);
          setEnableDropdown(true);

          if (!e?.value) {
            if (item.name === "kejaksaanTinggiID")
              setKejaksaanTinggiID(undefined);
            if (item.name === "kejaksaanUnitID") setKejaksaanUnitID(undefined);
          }
        }}
      />
    ),
  }));

  return (
    <div className="flex flex-col space-y-6">
      <PageHeader
        title="Filter Form"
        leftElement={[
          {
            element: (
              <div>
                <p className="text-sm text-gray-400">
                  Last Updated: 22/06/2023 14:30 WIB
                </p>
                <h1 className="text-2xl font-bold">Dashboard Analitik</h1>
              </div>
            ),
          },
        ]}
        rightElement={[
          ...selectFilter,
          {
            label: "Lihat Data",
            onClick: () => {
              setTimeout(() => {
                if (tableRef.current) {
                  tableRef.current.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                }
              }, 400);
              setTableSection(true);
            },
            isButton: true,
            variant: "primary",
          },
        ]}
      />
      {(kejaksaanUnitID || role === ROLE_ENUM.KEJARI) && (
        <ChartPidanaUmum
          isLoading={isLoadingChartData}
          chartData={chartData?.pidum}
        />
      )}
      <ChartPidanaKhusus
        isLoading={isLoadingChartData}
        chartData={chartData?.pidsus}
      />
      <div ref={tableRef}>
        {tableSection && <TableSectionIntel unitId={unitId} />}
      </div>
    </div>
  );
};
