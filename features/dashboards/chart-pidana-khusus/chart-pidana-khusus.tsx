"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/intel";
import { PidsusChartData } from "@/services/chart/chart-get.interface";

interface ChartPidanaKhususProps {
  chartData?: PidsusChartData;
  isLoading?: boolean;
}

export const ChartPidanaKhusus = ({
  chartData,
  isLoading = false,
}: ChartPidanaKhususProps) => {
  if (isLoading) {
    return (
      <div className="border-primary-300 rounded-lg border p-4">
        <div className="mb-4 h-8 w-1/3 animate-pulse rounded bg-gray-200" />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="h-64 w-full animate-pulse rounded-lg bg-gray-200" />
          <div className="h-64 w-full animate-pulse rounded-lg bg-gray-200" />
        </div>
      </div>
    );
  }

  if (!chartData) {
    return (
      <div className="border-primary-300 rounded-lg border p-4">
        <h2 className="text-primary-300 mb-4 text-2xl font-bold">
          <PERSON><PERSON><PERSON>
        </h2>
        <p className="text-gray-500">Data tidak tersedia.</p>
      </div>
    );
  }

  const doughnutLabels = [
    "Bareskrim",
    "Polri",
    "Polda",
    "Polres",
    "Polsek",
    "PPNS",
    "KPK",
    "Kejaksaan",
  ];

  const doughnutColors = [
    "#E82127",
    "#A36027",
    "#FFC107",
    "#FFF176",
    "#8BC34A",
    "#4CAF50",
    "#2196F3",
    "#9C27B0",
  ];

  const caseOrigin = chartData.case_origin_percentage ?? {};

  const doughnutData = doughnutLabels.map((label) => {
    const key = label.toLowerCase() as keyof typeof caseOrigin;
    return caseOrigin[key] ?? 0;
  });

  const dataDoughnutChart = {
    total: chartData.total ?? 0,
    labels: doughnutLabels,
    datasets: [
      {
        data: doughnutData,
        backgroundColor: doughnutColors,
        borderWidth: 0,
      },
    ],
  };

  const perStatus = chartData.total_perstatus ?? {};

  const dataBarChart = {
    total: chartData.total ?? 0,
    labels: ["Tersangka", "Terdakwa", "Terpidana", "DPO"],
    datasets: [
      {
        label: "Jumlah Perkara",
        data: [
          perStatus.total_tersangka ?? 0,
          perStatus.total_terdakwa ?? 0,
          perStatus.total_terpidana ?? 0,
          perStatus.total_dpo ?? 0,
        ],
        backgroundColor: [
          "rgba(243, 199, 62, 1)",
          "rgba(243, 131, 62, 1)",
          "rgba(240, 68, 56, 1)",
          "rgba(168, 35, 35, 1)",
        ],
        borderColor: [
          "rgba(243,199,62, 1)",
          "rgba(255, 99, 132, 1)",
          "rgba(75, 192, 192, 1)",
          "rgba(255, 159, 64, 1)",
        ],
        borderWidth: 1,
        borderRadius: 5,
      },
    ],
  };

  return (
    <div className="border-primary-300 rounded-lg border p-4">
      <h2 className="text-primary-300 mb-4 text-2xl font-bold">
        Pidana Khusus
      </h2>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <BarChart data={dataBarChart} />
        <DoughnutChart data={dataDoughnutChart} />
      </div>
    </div>
  );
};
