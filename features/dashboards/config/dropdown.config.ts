export type Unit = "kejagung" | "kejati" | "kejari";

export interface KejaksaanItem {
  id: number;
  name: string;
}

export interface DropdownConfigParams {
  kejaksaanTinggiList?: KejaksaanItem[];
  kejaksaanNegeriList?: KejaksaanItem[];
  setKejaksaanTinggiID?: (id: number) => void;
  setKejaksaanUnitID?: (id: number) => void;
  kejaksaanName: string;
  enableDropdown: boolean;
}

export const getDropdownConfig = ({
  kejaksaanTinggiList,
  kejaksaanNegeriList,
  enableDropdown,
  kejaksaanName,
  setKejaksaanTinggiID,
  setKejaksaanUnitID,
}: DropdownConfigParams): Record<
  Unit,
  Array<{
    placeholder: string;
    arrowIcon: boolean;
    name?: string;
    disabled?: boolean;
    onClick?: (value?: number) => void;
    options?: { label: string; value: string }[];
  }>
> => ({
  kejagung: [
    { placeholder: kejaksaanName, arrowIcon: false, disabled: true },
    {
      placeholder: "Kejak<PERSON><PERSON>",
      name: "kejaksaanTinggiID",
      onClick: (value) => {
        if (value) {
          setKejaksaanTinggiID?.(value);
        }
      },
      arrowIcon: true,
      options:
        kejaksaanTinggiList?.map((kejati) => ({
          label: kejati.name,
          value: kejati.id.toString(),
        })) || [],
    },
    {
      placeholder: "Kejaksaan Negri",
      name: "kejaksaanUnitID",
      onClick: (value) => {
        if (value) {
          setKejaksaanUnitID?.(value);
        }
      },
      arrowIcon: true,
      disabled: !enableDropdown,
      options:
        kejaksaanNegeriList?.map((kejari) => ({
          label: kejari.name,
          value: kejari.id.toString(),
        })) || [],
    },
  ],
  kejati: [
    { placeholder: kejaksaanName, arrowIcon: false, disabled: true },
    {
      placeholder: "Kejaksaan Negri",
      name: "kejaksaanUnitID",
      onClick: (value) => {
        if (value) {
          setKejaksaanUnitID?.(value);
        }
      },
      arrowIcon: true,
      options:
        kejaksaanNegeriList?.map((kejari) => ({
          label: kejari.name,
          value: kejari.id.toString(),
        })) || [],
    },
  ],
  kejari: [
    {
      placeholder: kejaksaanName,
      arrowIcon: false,
      disabled: true,
      options: [],
    },
  ],
});
