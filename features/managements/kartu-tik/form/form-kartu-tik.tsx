/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Card, Spinner } from "flowbite-react";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Accordion } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { DetailPerkara } from "./detail-perkara";
import { InformasiPribadi } from "./informasi-pribadi";
import { InformasiKontak } from "./informasi-kontak";
import { InformasiTambahan } from "./informasi-tambahan";
import { PasalDilanggar } from "./pasal-yang-dilanggar";
import { LatarBelakang } from "./latar-belakang";
import { PutusanPengadilan } from "./putusan-pengadilan";
import { InformasiTambahanLanjutan } from "./informasi-tambahan-lanjutan";
import { Otenti<PERSON>i } from "./otentikasi";
import { formSchema, FormValues } from "./schema";
import { FormInput } from "@/components/ui/form-input";
import { usePopupStore } from "@/stores/popup/popup";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createSuspect, updateSuspect } from "@/services/form/form-post";
import { useParams, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  DataCreateForm,
  ErrorResponse,
  FormKartuTikProps,
} from "./form-kartu-tik-interface";
import { useLoadingOverlayStore } from "@/stores/loading-overlay/loading-overlay";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(customParseFormat);
import { useEffect, useState, useCallback } from "react";
import {
  createDebouncedCheckLetterCode,
  useCheckLetterCodeMutation,
} from "@/utils/helpers/debounced-check-letter-code";
import {
  createDebouncedCheckRefNumber,
  useCheckRefNumberMutation,
} from "@/utils/helpers/debounced-check-ref-number";
import { routes } from "@/lib/routes";
import { Icon } from "@/components/shared/icon";
import { formatDateIfValid } from "@/utils/helpers/check-format-date";

export const FormKartuTik: React.FC<FormKartuTikProps> = ({
  mode = "create",
  initialValues,
}) => {
  const navigation = useRouter();
  const { id } = useParams<{ id: string }>();
  const queryClient = useQueryClient();
  const { data: dataSession } = useSession();
  const { openPopup, closePopup } = usePopupStore();
  const { show, hide } = useLoadingOverlayStore();
  const unitName = dataSession?.user?.kejaksaan_unit?.name;

  const [letterCodeError, setLetterCodeError] = useState<string>("");
  const [isCheckingLetterCode, setIsCheckingLetterCode] = useState(false);

  const [refNumberError, setRefNumberError] = useState<string>("");
  const [isCheckingRefNumber, setIsCheckingRefNumber] = useState(false);

  const FORMS = [
    {
      title: "Detail Perkara",
      content: <DetailPerkara />,
    },
    {
      title: "Informasi Pribadi",
      content: <InformasiPribadi />,
    },
    {
      title: "Informasi Kontak",
      content: <InformasiKontak />,
    },
    {
      title: "Informasi Tambahan",
      content: <InformasiTambahan />,
    },
    {
      title: "Pasal yang Dilanggar",
      content: <PasalDilanggar />,
    },
    {
      title: "Latar Belakang",
      content: <LatarBelakang />,
    },
    {
      title: "Putusan Pengadilan",
      content: <PutusanPengadilan />,
    },
    {
      title: "Informasi Tambahan",
      content: <InformasiTambahanLanjutan />,
    },
    {
      title: "Otentikasi",
      content: <Otentikasi />,
    },
  ];

  const submitSuspect = useMutation({
    mutationFn: (data: DataCreateForm) => createSuspect(data),
    onSuccess: () => {
      hide();
      queryClient.invalidateQueries({ queryKey: ["listForm"] });
      openPopup({
        title: "Berhasil Menyimpan Data",
        message: "Data berhasil disimpan",
        classNameContainerActions: "justify-end",
        icon: <Icon type="success" />,
        withHeader: false,
        size: "xl",
        actions: [
          {
            label: "Tutup",
            type: "button",
            onClick: () => {
              closePopup();
              navigation.replace(routes.managements.form.index);
            },
          },
        ],
      });
    },
    onError: (error: ErrorResponse) => {
      hide();
      openPopup({
        title: "Gagal Menyimpan Data",
        message: `Terjadi kesalahan saat menyimpan data, silahkan coba lagi. ${error?.response?.data?.message}`,
        classNameContainerActions: "justify-end",
        icon: <Icon type="error" />,
        size: "xl",
        actions: [
          {
            label: "Kembali",
            type: "button",
            onClick: () => closePopup(),
          },
        ],
      });
    },
  });

  const editSuspect = useMutation({
    mutationFn: (data: DataCreateForm) =>
      updateSuspect(Number(id as string), data),
    onSuccess: () => {
      hide();
      queryClient.invalidateQueries({ queryKey: ["listForm"] });
      openPopup({
        title: "Berhasil Mengubah Data",
        message: "Data berhasil diubah",
        classNameContainerActions: "justify-end",
        icon: <Icon type="success" />,
        withHeader: false,
        size: "xl",
        actions: [
          {
            label: "Tutup",
            type: "button",
            onClick: () => {
              closePopup();
              navigation.replace(routes.managements.form.index);
            },
          },
        ],
      });
    },
    onError: (error: ErrorResponse) => {
      hide();
      openPopup({
        title: "Gagal Mengubah Data",
        message: `Terjadi kesalahan saat mengubah data, silahkan coba lagi. ${error?.response?.data?.message}`,
        classNameContainerActions: "justify-end",
        icon: <Icon type="error" />,
        size: "xl",
        actions: [
          {
            label: "Kembali",
            type: "button",
            onClick: () => closePopup(),
          },
        ],
      });
    },
  });

  const handleCreateForm = (payload: DataCreateForm) => {
    show("Menyimpan data...");
    return mode === "create"
      ? submitSuspect.mutateAsync(payload)
      : editSuspect.mutateAsync(payload);
  };

  const getDefaultValues = (values?: DataCreateForm) => {
    const defaultValues = {
      ref_number: "",
      letter_code: "",
      suspect_type: "",
      suspect_origin: "",
      suspect_status: "",
      full_name: "",
      call_name: "",
      birth_place: "",
      birth_date: dayjs().subtract(10, "year").format("DD/MM/YYYY"),
      gender: "",
      kewarganegaraan: "",
      ethnic: "",
      province_id: "",
      city_id: "",
      subdistrict_id: "",
      ward_id: "",
      postal_code: "",
      address: "",
      phone_number: [{ value: "" }],
      type_idendity: "",
      identity_number: "",
      education_id: "",
      job_id: "",
      marital_status_id: "",
      address_work: "",
      residivist: "",
      kepartian: "",
      religion_id: "",
      pasal_dilanggar: "",
      latar_belakang: "",
      putusan_pengadilan_negri: "",
      putusan_pengadilan_tinggi: "",
      putusan_mahkama_agung: "",
      nama_orangtua: "",
      nama_kawanan: "",
      alamat_orangtua: "",
      informasi_lainnya: "",
      spk3_skpp_tempat_lahir: "",
      spk3_skpp_tanggal_lahir: "",
    };

    if (mode === "edit" && values) {
      return {
        ...defaultValues,
        ...values,
        phone_number: values.phone_number?.length
          ? values.phone_number.map((phone) => ({ value: phone }))
          : [{ value: "" }],
        province_id: values.province_id?.toString() || "",
        city_id: values.city_id?.toString() || "",
        subdistrict_id: values.subdistrict_id?.toString() || "",
        ward_id: values.ward_id?.toString() || "",
        education_id: values.education_id?.toString() || "",
        job_id: values.job_id?.toString() || "",
        marital_status_id: values.marital_status_id?.toString() || "",
        religion_id: values.religion_id?.toString() || "",
        birth_date: dayjs(values.birth_date).format("DD/MM/YYYY") || "",
        spk3_skpp_tanggal_lahir:
          dayjs(values.spk3_skpp_tanggal_lahir).format("DD/MM/YYYY") || "",
        residivist: values.residivist === false ? "0" : "1",
      };
    }

    return defaultValues;
  };

  const formManagement = useForm<FormValues>({
    mode: "onChange",
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(initialValues),
  });

  const {
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = formManagement;

  const letterCodeMutation = useCheckLetterCodeMutation({
    setError: setLetterCodeError,
    setIsChecking: setIsCheckingLetterCode,
  });

  const refNumberMutation = useCheckRefNumberMutation({
    setError: setRefNumberError,
    setIsChecking: setIsCheckingRefNumber,
  });

  const debouncedCheckLetterCode = useCallback(
    createDebouncedCheckLetterCode(letterCodeMutation.mutate, {
      setError: setLetterCodeError,
      setIsChecking: setIsCheckingLetterCode,
    }),
    [letterCodeMutation.mutate],
  );

  const debouncedCheckRefNumber = useCallback(
    createDebouncedCheckRefNumber(refNumberMutation.mutate, {
      setError: setRefNumberError,
      setIsChecking: setIsCheckingRefNumber,
    }),
    [refNumberMutation.mutate],
  );

  // Watch letter_code field for changes
  const letterCode = watch("letter_code");
  const refNumber = watch("ref_number");

  useEffect(() => {
    if (mode === "create" && letterCode) {
      debouncedCheckLetterCode(letterCode);
    }
  }, [letterCode, debouncedCheckLetterCode, mode]);

  useEffect(() => {
    if (mode === "create" && refNumber) {
      debouncedCheckRefNumber(refNumber);
    }
  }, [refNumber, debouncedCheckRefNumber, mode]);

  useEffect(() => {
    if (mode === "edit" && initialValues) {
      const values = getDefaultValues(initialValues);
      reset(values);
    }
  }, [initialValues, mode, reset]);

  const onSubmit = async (data: FormValues) => {
    if (
      isCheckingLetterCode ||
      isCheckingRefNumber ||
      letterCodeMutation.isPending ||
      refNumberMutation.isPending
    ) {
      return;
    }

    if (letterCodeError || refNumberError) {
      return;
    }
    const ids = [
      "religion_id",
      "marital_status_id",
      "job_id",
      "education_id",
      "ward_id",
      "subdistrict_id",
      "city_id",
      "province_id",
    ];
    const formattedData: any = { ...data };
    ids.forEach((id) => {
      formattedData[id as keyof FormValues] =
        data[id as keyof FormValues] === ""
          ? null
          : Number(data[id as keyof FormValues]);
    });
    formattedData.phone_number = formattedData.phone_number.map(
      (item: { value: string }) => item.value,
    );
    formattedData.residivist = Number(formattedData.residivist);
    formattedData.birth_date = formatDateIfValid(formattedData.birth_date);
    formattedData.spk3_skpp_tanggal_lahir = formatDateIfValid(
      formattedData.spk3_skpp_tanggal_lahir,
    );

    openPopup({
      title: `Konfirmasi ${mode === "edit" ? "Ubah" : "Simpan"} Data`,
      message: `Apakah kamu yakin ingin ${mode === "edit" ? "mengubah" : "menyimpan"} data ini?`,
      classNameContainerActions: "justify-end",
      withHeader: false,
      size: "xl",
      actions: [
        {
          label: "Kembali",
          type: "button",
          variant: "outline",
          onClick: () => closePopup(),
        },
        {
          label: `${mode === "edit" ? "Ubah" : "Simpan"} Data`,
          type: "submit",
          onClick: () => handleCreateForm(formattedData),
        },
      ],
    });
  };

  return (
    <FormProvider {...formManagement}>
      <Card className="border-main-2 space-y-4 shadow-none">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="flex justify-between">
            <h5 className="font-semibold">{unitName}</h5>
            <FormInput
              placeholder="D.IN.15"
              className="tik-number"
              id="ref_number"
              name="ref_number"
              inputType="text"
              sizing="md"
              isInvalid={!!errors.ref_number || !!refNumberError}
              errorMessage={errors.ref_number?.message || refNumberError}
              disabled={mode === "edit" || isCheckingRefNumber}
              rightElement={isCheckingRefNumber ? <Spinner size="xs" /> : null}
            />
          </div>
          <div className="flex flex-col items-center">
            <h6 className="text-sm font-semibold underline">
              KARTU TIK/TERSANGKA/TERDAKWA/TERPIDANA/DPO
            </h6>
            <div className="flex items-center gap-1">
              <p className="text-sm">Nomor : R -</p>
              <FormInput
                placeholder="Nomor Surat"
                className="ref-number"
                id="letter_code"
                name="letter_code"
                inputType="text"
                sizing="md"
                isInvalid={!!errors.letter_code || !!letterCodeError}
                errorMessage={errors.letter_code?.message || letterCodeError}
                disabled={mode === "edit" || isCheckingLetterCode}
                rightElement={
                  isCheckingLetterCode ? <Spinner size="xs" /> : null
                }
              />
            </div>
          </div>
          <Accordion items={FORMS} />
          <div className="mt-4 flex justify-end gap-4">
            <Button
              size="sm"
              variant="outline"
              onClick={() => navigation.push(routes.managements.form.index)}
            >
              Kembali
            </Button>
            <Button size="sm" type="submit">
              {mode === "create" ? "Simpan" : "Edit"} Data
            </Button>
          </div>
        </form>
      </Card>
    </FormProvider>
  );
};
