export interface ErrorResponse {
  message: string;
  statusCode: number;
  response: {
    data: {
      status_code: number;
      message: string;
      data: null;
      error: boolean;
      detail: {
        request_id: string;
        query: string;
        path: string;
        method: string;
        response_time: string;
        request_at: string;
        errors: null;
      };
    };
  };
}

export interface DataCreateForm {
  address: string;
  address_work: string;
  alamat_orangtua: string;
  birth_date: string;
  birth_place: string;
  call_name: string;
  card_tik: string;
  city_id: number;
  // country_id: number;
  education_id: number;
  ethnic: string;
  full_name: string;
  gender: string;
  identity_number: string;
  informasi_lainnya: string;
  job_id: number;
  kepartian: string;
  kewarganegaraan: string;
  latar_belakang: string;
  letter_code: string;
  marital_status_id: number;
  nama_kawanan: string;
  nama_orangtua: string;
  pasal_dilanggar: string;
  phone_number: string[];
  postal_code: string;
  province_id: number;
  putusan_mahkama_agung: string;
  putusan_pengadilan_negri: string;
  putusan_pengadilan_tinggi: string;
  ref_number: string;
  religion_id: number;
  residivist: boolean;
  spk3_skpp_tanggal_lahir: string;
  spk3_skpp_tempat_lahir: string;
  subdistrict_id: number;
  suspect_origin: string;
  suspect_status: string;
  suspect_type: string;
  type_idendity: string;
  ward_id: number;
}

export interface FormKartuTikProps {
  mode?: "create" | "edit";
  initialValues?: DataCreateForm;
}
