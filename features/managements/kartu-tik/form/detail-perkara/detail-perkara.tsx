import { FormInput } from "@/components/ui/form-input";
import React, { useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { FormValues } from "../schema";
import {
  DROPDOWN_ASAL_PERKARA,
  DROPDOWN_JENIS_PERKARA,
  DROPDOWN_STATUS_PERKARA,
} from "@/constants";
import { useSession } from "next-auth/react";

export const DetailPerkara = () => {
  const { data: dataSession } = useSession();
  const role = dataSession?.user?.role;

  const {
    formState: { errors },
    setValue,
  } = useFormContext<FormValues>();

  useEffect(() => {
    if (role) {
      setValue(
        "suspect_type",
        DROPDOWN_JENIS_PERKARA.find((item) => item.value === role)
          ?.value as string,
      );
    }
  }, [role]);

  const disabledSuspectType = ["pidum", "pidsus"].includes(role as string);

  return (
    <div className="flex justify-between gap-2">
      <FormInput
        placeholder="<PERSON>lih <PERSON>"
        id="suspect_type"
        name="suspect_type"
        inputType="react-select"
        sizing="md"
        disabled={disabledSuspectType}
        isInvalid={!!errors.suspect_type}
        errorMessage={errors.suspect_type?.message}
        options={DROPDOWN_JENIS_PERKARA.filter((item) => item.value !== "")}
        reactSelectProps={{
          isClearable: true,
        }}
      />
      <FormInput
        placeholder="Pilih Asal Perkara"
        id="suspect_origin"
        name="suspect_origin"
        inputType="react-select"
        sizing="md"
        isInvalid={!!errors.suspect_origin}
        errorMessage={errors.suspect_origin?.message}
        options={DROPDOWN_ASAL_PERKARA.filter((item) => item.value !== "")}
        reactSelectProps={{
          isClearable: true,
        }}
      />
      <FormInput
        placeholder="Pilih Status Perkara"
        id="suspect_status"
        name="suspect_status"
        inputType="react-select"
        sizing="md"
        isInvalid={!!errors.suspect_status}
        errorMessage={errors.suspect_status?.message}
        options={DROPDOWN_STATUS_PERKARA.filter((item) => item.value !== "")}
        reactSelectProps={{
          isClearable: true,
        }}
      />
    </div>
  );
};
