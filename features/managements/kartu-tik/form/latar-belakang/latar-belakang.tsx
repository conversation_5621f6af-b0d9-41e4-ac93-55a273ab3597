import { FormInput } from "@/components/ui/form-input";
import React from "react";
import { useFormContext } from "react-hook-form";
import { FormValues } from "../schema";

export const LatarBelakang = () => {
  const {
    formState: { errors },
  } = useFormContext<FormValues>();

  return (
    <div className="flex">
      <FormInput
        placeholder="Masukkan Latar Belakang"
        id="latar_belakang"
        name="latar_belakang"
        inputType="textarea"
        sizing="md"
        isInvalid={!!errors.latar_belakang}
        errorMessage={errors.latar_belakang?.message}
      />
    </div>
  );
};
