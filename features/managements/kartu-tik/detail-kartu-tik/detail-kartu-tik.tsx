"use client";
import { PageHeader } from "@/components/ui/page-header/page-header";
import { routes } from "@/lib/routes";
import { UserData } from "@/types/auth";
import React from "react";
import {
  HiOutlineArrowLeft,
  HiOutlineDownload,
  HiOutlinePrinter,
  HiPencil,
} from "react-icons/hi";
import { CardDetailTik } from "./card-detail-tik";
import { useParams } from "next/navigation";
import { useHandleRequestEdit } from "@/lib/hooks/use-handle-request-edit";
import { getSuspectDetail } from "@/services/form/form-get";
import { Spinner } from "flowbite-react";
import { useQuery } from "@tanstack/react-query";
import { ROLE_ENUM, STATUS_APPROVED_ENUM } from "@/constants";

export interface DetailKartuTikProps {
  userData?: UserData;
}

export const DetailKartuTik = ({ userData }: DetailKartuTikProps) => {
  const { id } = useParams<{ id: string }>();
  const role = userData?.role;
  const { handleEdit: handleClickEdit } = useHandleRequestEdit();

  const { data: detail, isLoading } = useQuery({
    queryKey: ["suspectDetail", id ?? ""],
    queryFn: () => getSuspectDetail(parseInt(id ?? "0", 10)),
  });

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-6">
      <PageHeader
        className="border-b border-gray-300 pb-6"
        title={`Kartu TIK [${detail?.suspect_identity?.full_name}]`}
        backHref={
          role === ROLE_ENUM.INTEL
            ? routes.dashboard
            : routes.managements.form.index
        }
        backIcon={<HiOutlineArrowLeft className="h-5 w-5" />}
        rightElement={[
          {
            icon: <HiOutlinePrinter className="h-5 w-5" />,
            label: "Cetak Dokumen",
            isButton: true,
            onClick: () => console.log("Cetak"),
          },
          {
            icon: <HiOutlineDownload className="h-5 w-5" />,
            label: "Download",
            variant: "outline",
            isButton: true,
            onClick: () => console.log("Download"),
          },
          ...(role !== "intel"
            ? [
                {
                  icon: <HiPencil className="h-5 w-5" />,
                  label: "Edit Data",
                  variant: "outline" as const,
                  isButton: true,
                  onClick: () => {
                    handleClickEdit(
                      Number(id),
                      detail?.status_approval ?? STATUS_APPROVED_ENUM.PENDING,
                    );
                  },
                },
              ]
            : []),
        ]}
      />
      {detail && <CardDetailTik detail={detail} />}
    </div>
  );
};
