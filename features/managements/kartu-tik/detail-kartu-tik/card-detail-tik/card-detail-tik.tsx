import { Card } from "flowbite-react";
import { InfoSection } from "./info-section";
import dayjs from "dayjs";
import {
  CITIZENSHIP_LABEL,
  GENDER_LABEL,
  STATUS_PERKARA_ENUM,
  STATUS_PERKARA_LABEL,
} from "@/constants";
import { OtentikasiSection } from "./otentikasi-section";
import { SuspectDetailData } from "@/services/form/form-get.interface";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";

interface CardDetailTikProps {
  detail: SuspectDetailData;
}

export const CardDetailTik = ({ detail }: CardDetailTikProps) => {
  const identity = detail?.suspect_identity;
  const history = detail?.suspect_history;

  const identityList = [
    { label: "Nama Lengkap", value: identity?.full_name },
    { label: "Nama samaran/panggilan", value: identity?.nickname },
    {
      label: "Tempat/Tgl lahir/Umur",
      value: `${identity?.place_of_birth || "-"}, ${dayjs(identity?.date_of_birth).format("DD-MM-YYYY")} (${dayjs().diff(identity?.date_of_birth, "year")} tahun)`,
    },
    {
      label: "Jenis Kelamin",
      value: GENDER_LABEL[identity?.gender ?? ""] || identity?.gender,
    },
    { label: "Bangsa/Suku", value: identity?.ethnic },
    {
      label: "Kewarganegaraan",
      value:
        CITIZENSHIP_LABEL[identity?.citizenship ?? ""] || identity?.citizenship,
    },
    { label: "Alamat/Tempat Tinggal", value: identity?.address },
    { label: "Nomor Telepon/HP", value: identity?.phone_numbers },
    { label: "Nomor KTP", value: identity?.identity_number },
    { label: "Agama/Kepercayaan", value: identity?.religion },
    { label: "Pekerjaan", value: identity?.job },
    { label: "Alamat Kantor", value: identity?.office_address },
    { label: "Status Perkawinan", value: identity?.marital_status },
    { label: "Kepartaian", value: identity?.organization_politic },
    { label: "Pendidikan", value: identity?.education },
  ];

  const historyList = [
    {
      label: "Kejahatan/pelanggaran yang dilakukan",
      children: [
        {
          label: "Kasus posisi singkat/pasal yang dilanggar",
          value: history?.article_violated,
        },
        {
          label: "Latar belakang & akibat peristiwa/kerugian",
          value: history?.background_story,
        },
        {
          label: "SP3/SKPP/RJ (No / Tgl)",
          value: `${history?.sp3_skkp} (${dayjs(history?.sp3_skkp_date).format("DD-MM-YYYY")})`,
        },
        {
          label: "Putusan pengadilan Negeri",
          value: history?.court_decision_pn,
        },
        {
          label: "Putusan Pengadilan Tinggi",
          value: history?.court_decision_pt,
        },
        { label: "Putusan Mahkamah Agung", value: history?.court_decision_ma },
      ],
    },
    {
      label: "Nama orang tua/Saudara/Alamat",
      value: `${history?.parent_name || "-"}`,
    },
    {
      label: "Nama Kawan yang dikenal",
      value: `${history?.friend_name || "-"}`,
    },
    {
      label: "Lain-lain",
      value: history?.other || "-",
    },
  ];

  const sectionList = [
    { title: "I. IDENTITAS", data: identityList },
    { title: "II. RIWAYAT PERKARA", data: historyList },
  ];

  return (
    <Card className="border-main-2 space-y-8 p-6 shadow-none">
      <div className="flex justify-between">
        <h5 className="text-lg font-bold">{detail?.kejaksaan_unit?.name}</h5>
        <div className="fw-medium text-base">{detail?.ref_number}</div>
      </div>
      <div className="flex flex-col items-center">
        <h6 className="text-lg font-semibold capitalize">
          KARTU TIK{" "}
          <span>
            {Object.values(STATUS_PERKARA_ENUM).map((status, index, array) => (
              <span
                key={status}
                className={twMerge(
                  "uppercase",
                  status === detail?.suspect_status
                    ? "text-dark font-bold"
                    : "text-gray-400 line-through",
                )}
              >
                {STATUS_PERKARA_LABEL[status]}
                {index < array.length - 1 && "/"}
              </span>
            ))}
          </span>
        </h6>
        <div className="flex items-center gap-1">
          <p className="text-sm">Nomor : R - {detail?.letter_code}</p>
        </div>
      </div>

      {sectionList.map((section, index) => (
        <InfoSection key={index} title={section.title} data={section.data} />
      ))}

      <hr className="my-6" />

      <OtentikasiSection />
    </Card>
  );
};
