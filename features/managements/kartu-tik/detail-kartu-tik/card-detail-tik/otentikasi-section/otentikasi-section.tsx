import { Card } from "flowbite-react";
import { FC } from "react";
import { BiFile } from "react-icons/bi";
import {
  BasicAuthItem,
  FingerLabel,
  FingerprintData,
  PalmHandData,
} from "./otentikasi-section-type";
import { BiImage } from "react-icons/bi";
import { PlaceholderImageCardProps } from "./otentikasi-section-type";

const HeaderSection: FC = () => (
  <div className="flex items-center gap-1 text-green-400">
    <BiFile className="mr-2" size={36} />
    <h5 className="text-2xl font-bold text-green-600">Otentikasi</h5>
  </div>
);

const ImageGridSection: FC<{
  title: string;
  items: PalmHandData[];
}> = ({ title, items }) => (
  <div className="flex w-full flex-col items-center gap-6 rounded-xl border border-gray-200 px-8 py-6 shadow-none">
    <div className="text-center text-xl font-semibold text-green-600">
      {title}
    </div>
    <div className="grid w-full grid-cols-2 gap-24">
      {items.map(({ title, label }, index) => (
        <Card
          key={index}
          className="flex w-full flex-col items-center rounded-xl border border-gray-200 shadow-none"
        >
          <div className="text-center text-lg font-semibold text-green-600">
            {title}
          </div>
          <PlaceholderImageCard label={label || "Belum tersedia"} />
        </Card>
      ))}
    </div>
  </div>
);

export const OtentikasiSection: FC = () => {
  const basicAuthItems: BasicAuthItem[] = [
    { title: "Foto", label: "No image available" },
    { title: "Tanda Tangan", label: "No signature available" },
  ];

  const fingerprintData: FingerprintData = {
    leftHand: {
      title: "Tangan Kiri",
      fingers: [
        { label: "Ibu Jari" },
        { label: "Telunjuk" },
        { label: "Tengah" },
        { label: "Manis" },
        { label: "Kelingking" },
      ],
    },
    rightHand: {
      title: "Tangan Kanan",
      fingers: [
        { label: "Ibu Jari" },
        { label: "Telunjuk" },
        { label: "Tengah" },
        { label: "Manis" },
        { label: "Kelingking" },
      ],
    },
  };

  const palmHandData: PalmHandData[] = [
    { title: "Tangan Kiri", label: "" },
    { title: "Tangan Kanan", label: "" },
  ];

  const irisEyeData: PalmHandData[] = [
    { title: "Mata Kiri", label: "" },
    { title: "Mata Kanan", label: "" },
  ];

  return (
    <section className="flex flex-col space-y-8">
      <HeaderSection />

      <div className="grid grid-cols-2 gap-24">
        {basicAuthItems.map(({ title, label }, index) => (
          <Card
            key={index}
            className="flex flex-col items-center rounded-xl border border-gray-200 shadow-none"
          >
            <div className="text-center text-lg font-semibold text-green-600">
              {title}
            </div>
            <PlaceholderImageCard label={label} />
          </Card>
        ))}
      </div>

      <Card className="flex flex-col items-center rounded-xl border border-gray-200 shadow-none">
        <div className="text-center text-xl font-semibold text-green-600">
          Sidik Jari
        </div>
        <div className="flex flex-col space-y-6">
          {Object.entries(fingerprintData).map(([key, group]) => (
            <div key={key} className="flex flex-col items-center space-y-4">
              <div className="text-center text-base font-semibold text-green-600">
                {group.title}
              </div>
              <div className="grid grid-cols-5 gap-6">
                {group.fingers.map((finger: FingerLabel, idx: number) => (
                  <PlaceholderImageCard key={idx} label={finger.label} />
                ))}
              </div>
            </div>
          ))}
        </div>
      </Card>

      <ImageGridSection title="Telapak Tangan" items={palmHandData} />

      <ImageGridSection title="Iris Mata" items={irisEyeData} />
    </section>
  );
};

export const PlaceholderImageCard: FC<PlaceholderImageCardProps> = ({
  label,
}) => (
  <div className="flex h-52 w-52 flex-col items-center justify-center space-y-3 rounded-xl border border-green-200 bg-gray-100">
    <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gray-200">
      <BiImage size={42} className="text-gray-400" />
    </div>
    <div className="text-center font-semibold text-gray-400">{label}</div>
  </div>
);
