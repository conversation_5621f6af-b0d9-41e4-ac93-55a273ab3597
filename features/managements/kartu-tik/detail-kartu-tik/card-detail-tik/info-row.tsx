import { twMerge } from "flowbite-react/helpers/tailwind-merge";

interface InfoRowProps {
  prefix: string;
  label: string;
  value?: string | number | null;
  isChild?: boolean;
  isParent?: boolean;
}

export const InfoRow = ({
  prefix,
  label,
  value,
  isChild = false,
  isParent = false,
}: InfoRowProps) => (
  <tr>
    <td
      className={twMerge(
        "w-2/5 py-2 text-sm font-medium text-gray-900",
        isChild ? "pl-5" : "",
      )}
    >
      {prefix}. {label}
    </td>
    {!isParent && (
      <>
        <td className="px-2 py-2 text-sm font-medium text-gray-900">{":"}</td>
        <td className="w-3/5 py-2 text-sm font-medium text-gray-900">
          {value || "-"}
        </td>
      </>
    )}
  </tr>
);
