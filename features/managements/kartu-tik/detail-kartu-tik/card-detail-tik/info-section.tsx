import { Fragment } from "react";
import { InfoRow } from "./info-row";

interface InfoSectionData {
  label: string;
  value?: string | number | null;
  children?: { label: string; value?: string | number | null }[];
}

interface InfoSectionProps {
  title: string;
  data: InfoSectionData[];
}

export const InfoSection = ({ title, data }: InfoSectionProps) => (
  <section className="space-y-2">
    <h5 className="text-lg font-bold">{title}</h5>
    <div className="mx-4 my-3">
      <table className="w-full table-auto">
        <tbody>
          {data.map((item, index) => (
            <Fragment key={index}>
              <InfoRow
                prefix={String(index + 1)}
                label={item.label}
                value={item.value}
                isParent={!!item.children?.length}
              />
              {item.children?.map((child, childIndex) => (
                <InfoRow
                  key={`${index}-${childIndex}`}
                  prefix={String.fromCharCode(97 + childIndex)} // a, b, c, ...
                  label={child.label}
                  value={child.value}
                  isChild
                />
              ))}
            </Fragment>
          ))}
        </tbody>
      </table>
    </div>
  </section>
);
