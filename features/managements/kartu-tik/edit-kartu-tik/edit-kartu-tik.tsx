"use client";
import { PageHeader } from "@/components/ui/page-header/page-header";
import React, { useEffect } from "react";
import {
  HiOutlineArrowLeft,
  HiOutlineDownload,
  HiOutlinePrinter,
} from "react-icons/hi";
import { FormKartuTik } from "../form";
import { routes } from "@/lib/routes";
import { useParams, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { getSuspectEditDetail } from "@/services/form/form-get";
import { useLoadingOverlayStore } from "@/stores/loading-overlay/loading-overlay";
import { DataCreateForm } from "../form/form-kartu-tik-interface";

export const EditKartuTik = () => {
  const router = useRouter();
  const { show, hide } = useLoadingOverlayStore();
  const { id } = useParams<{ id: string }>();

  const {
    data: detail,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["suspectDetail", id ?? ""],
    queryFn: () => getSuspectEditDetail(parseInt(id ?? "0", 10)),
    enabled: !!id,
  });

  useEffect(() => {
    if (error) {
      const status =
        error && typeof error === "object" && "status" in error
          ? error.status
          : undefined;
      if (status === 403) {
        router.back();
      }
    }
  }, [error]);

  useEffect(() => {
    if (isLoading) {
      show();
    } else {
      hide();
    }
  }, [isLoading, show, hide]);

  return (
    <>
      <div className="flex flex-col space-y-6">
        <PageHeader
          className="border-b border-gray-300 pb-6"
          title="Edit Kartu TIK"
          backHref={routes.managements.form.index}
          backIcon={<HiOutlineArrowLeft className="h-5 w-5" />}
          rightElement={[
            {
              icon: <HiOutlinePrinter className="h-5 w-5" />,
              label: "Cetak Dokumen",
              isButton: true,
              onClick: () => console.log("Cetak"),
            },
            {
              icon: <HiOutlineDownload className="h-5 w-5" />,
              label: "Download",
              variant: "outline",
              isButton: true,
              onClick: () => console.log("Download"),
            },
          ]}
        />
        <FormKartuTik mode="edit" initialValues={detail as DataCreateForm} />
      </div>
    </>
  );
};
