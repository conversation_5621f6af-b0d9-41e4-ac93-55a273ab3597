"use client";
import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { HiP<PERSON> } from "react-icons/hi";
import { Tabbar } from "@/components/ui/tabbar";
import { SearchInput } from "@/components/ui";
import { PageHeader } from "@/components/ui/page-header/page-header";
import { ReactSelect } from "@/components/ui/react-select/react-select";
import { UserData } from "@/types/auth";
import { routes } from "@/lib/routes";
import {
  DROPDOWN_ASAL_PERKARA,
  DROPDOWN_JENIS_PERKARA,
  DROPDOWN_STATUS_PERKARA,
} from "@/constants";
import { debounce } from "lodash";
import { TableSection } from "@/features/home/<USER>/table-section";
import { TableRequestEditForm } from "@/features/home/<USER>/table-request-edit-form";
import { ActionButton } from "@/components/ui/page-header/page-header-types";

export interface ManagementFormProps {
  userData?: UserData;
}

export const ManagementForm = ({ userData }: ManagementFormProps) => {
  const [keyword, setKeyword] = useState("");
  const [query, setQuery] = useState<{
    asal_perkara: string;
    jenis_perkara: string;
    status_perkara: string;
    keyword: string;
  }>({
    asal_perkara: "",
    jenis_perkara: "",
    status_perkara: "",
    keyword: "",
  });

  const authorizedCreateForm = useMemo(() => {
    return ["pidsus", "pidum"].includes(userData?.role as string);
  }, [userData?.role]);

  const handleSearchChange = useMemo(
    () =>
      debounce((value: string) => {
        setQuery((prev) => ({
          ...prev,
          keyword: value,
        }));
      }, 300),
    [],
  );

  const handleSelectChange = useCallback(
    (field: keyof typeof query, value: string) => {
      setQuery((prev) => ({
        ...prev,
        [field]: value,
      }));
    },
    [],
  );

  return (
    <div className="flex flex-col space-y-6">
      <PageHeader
        className="border-b border-gray-300 pb-6"
        title="Form Management"
        rightElement={[
          {
            element: (
              <div className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700">
                {userData?.kejaksaan_unit.name ?? "-"}
              </div>
            ),
          },
        ]}
      />
      <PageHeader
        title="Filter Form"
        leftElement={[
          {
            element: (
              <SearchInput
                value={keyword}
                placeholder="Cari berdasarkan NAMA/NIK/TTL"
                onChange={(e) => {
                  setKeyword(e.target.value);
                  handleSearchChange(e.target.value);
                }}
              />
            ),
          },
        ]}
        rightElement={[
          {
            element: (
              <ReactSelect
                isClearable
                options={DROPDOWN_STATUS_PERKARA}
                placeholder="Status Perkara"
                onChange={(e) =>
                  handleSelectChange("status_perkara", e?.value || "")
                }
              />
            ),
          },
          {
            element: (
              <ReactSelect
                isClearable
                options={DROPDOWN_ASAL_PERKARA}
                placeholder="Asal Perkara"
                onChange={(e) =>
                  handleSelectChange("asal_perkara", e?.value || "")
                }
              />
            ),
          },
          {
            element: (
              <ReactSelect
                isClearable
                options={DROPDOWN_JENIS_PERKARA}
                placeholder="Jenis Perkara"
                onChange={(e) =>
                  handleSelectChange("jenis_perkara", e?.value || "")
                }
              />
            ),
          },
          ...((authorizedCreateForm
            ? [
                {
                  icon: <HiPlus className="mr-2 h-5 w-5" />,
                  label: "Tambah Form",
                  href: routes.managements.form.create,
                  isButton: true,
                  variant: "primary",
                },
              ]
            : []) as ActionButton[]),
        ]}
      />
      <div className="">
        <Tabbar
          items={[
            {
              title: "Form Baru",
              content: (
                <TableSection
                  manualPagination
                  onPageIndexChange={(pageIndex) => {
                    setQuery((prev) => ({
                      ...prev,
                      page: pageIndex,
                    }));
                  }}
                  params={{
                    unitId: userData?.kejaksaan_unit.id || 0,
                    query: { ...query, keyword: query.keyword.trim() },
                  }}
                />
              ),
            },
            {
              title: "Request Edit Form",
              content: (
                <TableRequestEditForm
                  params={{
                    unitId: userData?.kejaksaan_unit.id || 0,
                    query: { ...query, keyword: query.keyword.trim() },
                  }}
                />
              ),
            },
          ]}
        />
      </div>
    </div>
  );
};
