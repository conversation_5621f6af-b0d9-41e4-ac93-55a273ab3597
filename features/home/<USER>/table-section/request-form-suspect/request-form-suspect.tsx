import { ErrorResponse } from "@/lib/error-handler/error-handler.interface";
import { Fragment, useState } from "react";
import { usePopupStore } from "@/stores/popup/popup";
import { requestFormSuspect } from "@/services/form/form-post";
import { RequestFormSuspectData } from "@/services/form/form-post.interface";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button/button";
import { useLoadingOverlayStore } from "@/stores/loading-overlay/loading-overlay";
import { Icon } from "@/components/shared/icon";

interface RequestFormSuspectProps {
  id: number;
}

export const RequestFormSuspect = ({ id }: RequestFormSuspectProps) => {
  const { openPopup, closePopup } = usePopupStore();
  const { show, hide } = useLoadingOverlayStore();
  const queryClient = useQueryClient();

  const [noteRequestForm, setNoteRequestForm] = useState<string>("");
  const submitRequestFormSuspect = useMutation({
    mutationFn: (data: RequestFormSuspectData) => requestFormSuspect(data),
    onSuccess: () => {
      hide();
      queryClient.invalidateQueries({ queryKey: ["listForm"] });
      openPopup({
        title: "Berhasil Menyimpan Data",
        message: "Data berhasil disimpan",
        classNameContainerActions: "justify-end",
        icon: <Icon type="success" />,
        withHeader: false,
        size: "xl",
        actions: [
          {
            label: "Tutup",
            type: "button",
            onClick: () => {
              closePopup();
            },
          },
        ],
      });
    },
    onError: (error: ErrorResponse) => {
      hide();
      openPopup({
        title: "Gagal Menyimpan Data",
        message: `Terjadi kesalahan saat menyimpan data, silahkan coba lagi. ${error.message}`,
        classNameContainerActions: "justify-end",
        icon: <Icon type="error" />,
        size: "xl",
        actions: [
          {
            label: "Kembali",
            type: "button",
            onClick: () => closePopup(),
          },
        ],
      });
    },
  });
  return (
    <Fragment>
      <div className="mt-4">
        <label
          htmlFor="note"
          className="mb-1 block text-sm font-medium text-gray-700"
        >
          Alasan Perubahan Data
        </label>
        <textarea
          id="note"
          className="w-full rounded-md border border-gray-300 p-2 text-sm"
          placeholder="Masukkan alasan perubahan"
          rows={4}
          onChange={(e) => {
            setNoteRequestForm(e.target.value);
          }}
        />
      </div>
      <div className="mt-6 flex items-center justify-end gap-3">
        <Button
          variant="outline"
          onClick={() => {
            closePopup();
          }}
        >
          Kembali
        </Button>
        <Button
          onClick={() => {
            show("Mengajukan perubahan data...");
            submitRequestFormSuspect.mutate({
              suspect_id: id,
              note: noteRequestForm,
            });
          }}
          disabled={noteRequestForm.trim() === ""}
        >
          Ajukan Perubahan
        </Button>
      </div>
    </Fragment>
  );
};
