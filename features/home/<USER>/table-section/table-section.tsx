"use client";
import { Table } from "@/components/ui/table";
import { useQuery } from "@tanstack/react-query";
import { getListForm } from "@/services/form/form-get";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "flowbite-react";
import { ListFormData } from "@/services/form/form-get.interface";
import { HiOutlinePencil, HiOutlineEye } from "react-icons/hi";
import Link from "next/link";
import { routes } from "@/lib/routes";
import {
  badgeJenisPerkara,
  badgeStatusPerkara,
} from "@/utils/helpers/badge-helper";
import { STATUS_APPROVED_ENUM, STATUS_PERKARA_ENUM } from "@/constants";
import { useSession } from "next-auth/react";
import { useHandleRequestEdit } from "@/lib/hooks/use-handle-request-edit";

export interface TableSectionProps {
  total_data?: number;
  onPageIndexChange?: (pageIndex: number) => void;
  manualPagination?: boolean;
  params: {
    unitId: number;
    query?: {
      keyword?: string;
      status_perkara?: string;
      jenis_perkara?: string;
      asal_perkara?: string;
      limit?: number;
      page?: number;
      sort?: string;
    };
  };
}

export default function TableSection(props: TableSectionProps) {
  const { data: dataSession } = useSession();
  const canEditAccess = dataSession?.user?.user_access?.can_edit ?? false;

  const { handleEdit: handleClickEdit } = useHandleRequestEdit();

  const {
    data: listForm,
    isLoading: isLoadingListForm,
    // isError: isErrorListForm,
  } = useQuery({
    queryKey: ["listForm", { ...props.params }],
    queryFn: ({ queryKey }) => {
      const [, params] = queryKey;
      if (typeof params === "string") {
        throw new Error("Invalid params type");
      }
      return getListForm(params);
    },
  });

  const columns: ColumnDef<ListFormData>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => info.getValue(),
      meta: {
        className: "text-nowrap text-start sticky left-0 bg-white",
        classNameHead: "bg-neutral-17 text-nowrap sticky left-0",
      },
    },
    {
      accessorKey: "umur",
      header: "Umur",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "alamat",
      header: "Alamat",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "pasal",
      header: () => (
        <span className="inline-block w-full text-center">Pasal</span>
      ),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "status_perkara",
      header: () => (
        <span className="inline-block w-full text-center">Status Perkara</span>
      ),
      cell: (info) =>
        badgeStatusPerkara(
          info.row.original.status_perkara as STATUS_PERKARA_ENUM,
        ),
    },
    {
      accessorKey: "jenis_perkara",
      header: () => (
        <span className="inline-block w-full text-center">Jenis Perkara</span>
      ),
      cell: (info) => badgeJenisPerkara(info.row.original.jenis_perkara),
    },
    {
      accessorKey: "asal_perkara",
      header: () => (
        <span className="inline-block w-full text-center">Asal Perkara</span>
      ),
      cell: (info) => (
        <Badge className="mx-auto w-fit rounded-2xl px-4 py-1.5 capitalize">
          {info.getValue() as string}
        </Badge>
      ),
    },
    {
      accessorKey: "tanggal",
      header: "Tanggal Request",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "catatan",
      header: "Catatan",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "action",
      header: "Aksi",
      meta: {
        className: "text-nowrap text-start sticky right-0 bg-white",
        classNameHead: "bg-neutral-17 text-nowrap sticky right-0",
      },
      cell: ({ row }) => (
        <div className="flex items-center justify-center space-x-4">
          {canEditAccess && (
            <button
              onClick={() => {
                handleClickEdit(
                  row.original.id,
                  row.original.status_approved as STATUS_APPROVED_ENUM,
                );
              }}
              className="text-neutral-14 hover:text-neutral-11 cursor-pointer"
            >
              <HiOutlinePencil className="h-5 w-5" />
            </button>
          )}
          <Link
            href={routes.managements.form.view(row.original?.id ?? 0)}
            className="text-neutral-14 hover:text-neutral-11"
          >
            <HiOutlineEye className="h-5 w-5" />
          </Link>
        </div>
      ),
    },
  ];

  return (
    <Table
      total_data={listForm?.total_data}
      manualPagination={props.manualPagination}
      onPageIndexChange={props.onPageIndexChange}
      data={listForm?.data ?? []}
      columns={columns}
      pageCount={listForm?.total_page}
      isLoading={isLoadingListForm}
    />
  );
}
