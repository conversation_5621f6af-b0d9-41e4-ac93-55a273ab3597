"use client";
import { Table } from "@/components/ui/table";
import { useQuery } from "@tanstack/react-query";
import { getListRequestEditForm } from "@/services/form/form-get";
import { ColumnDef } from "@tanstack/react-table";
import { ListRequestEditFormData } from "@/services/form/form-get.interface";
import { HiOutlineEye, HiOutlinePencil } from "react-icons/hi";
import Link from "next/link";
import { routes } from "@/lib/routes";
import {
  badgeJenisPerkara,
  badgeStatusPerkara,
  badgeStatusRequestEditForm,
} from "@/utils/helpers/badge-helper";
import { STATUS_APPROVED_ENUM, STATUS_PERKARA_ENUM } from "@/constants";
import { useSession } from "next-auth/react";
import { useHandleRequestEdit } from "@/lib/hooks/use-handle-request-edit";
import { Button } from "flowbite-react";
import { useApproveRejectRequestForm } from "@/lib/hooks/use-approve-reject-request-form";

export interface TableRequestEditFormProps {
  onPageIndexChange?: (pageIndex: number) => void;
  manualPagination?: boolean;
  params: {
    unitId: number;
    query?: {
      keyword?: string;
      status_perkara?: string;
      jenis_perkara?: string;
      asal_perkara?: string;
      limit?: number;
      page?: number;
      sort?: string;
    };
  };
}

export default function TableRequestEditForm(props: TableRequestEditFormProps) {
  const { data: dataSession } = useSession();
  const userAccess = dataSession?.user?.user_access;
  const canEditAccess = userAccess?.can_edit ?? false;
  const canAcceptRequest = userAccess?.can_accept_request ?? false;
  const { handleEdit: handleClickEdit } = useHandleRequestEdit();

  const { handleApproveReject } = useApproveRejectRequestForm();

  const { data: listFormRequestEdit, isLoading: isLoadingListFormRequestEdit } =
    useQuery({
      queryKey: ["listRequestEditForm", { ...props.params }],
      queryFn: ({ queryKey }) => {
        const [, params] = queryKey;
        if (typeof params === "string") {
          throw new Error("Invalid params type");
        }
        return getListRequestEditForm(params);
      },
    });

  const columns: ColumnDef<ListRequestEditFormData>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => info.getValue(),
      meta: {
        className: "text-nowrap text-start sticky left-0 bg-white",
        classNameHead: "bg-neutral-17 text-nowrap sticky left-0",
      },
    },
    {
      accessorKey: "umur",
      header: "Umur",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "alamat",
      header: "Alamat",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "pasal",
      header: () => (
        <span className="inline-block w-full text-center">Pasal</span>
      ),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "status_perkara",
      header: () => (
        <span className="inline-block w-full text-center">Status Perkara</span>
      ),
      cell: (info) =>
        badgeStatusPerkara(
          info.row.original.status_perkara as STATUS_PERKARA_ENUM,
        ),
    },
    {
      accessorKey: "jenis_perkara",
      header: () => (
        <span className="inline-block w-full text-center">Jenis Perkara</span>
      ),
      cell: (info) => badgeJenisPerkara(info.row.original.jenis_perkara),
    },
    {
      accessorKey: "tanggal",
      header: "Tanggal Request",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "catatan",
      header: "Catatan",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) =>
        badgeStatusRequestEditForm(
          row.original.status_request as STATUS_APPROVED_ENUM,
        ),
    },
    {
      accessorKey: "action",
      header: "Aksi",
      meta: {
        className: "text-nowrap text-start sticky right-0 bg-white",
        classNameHead: "bg-neutral-17 text-nowrap sticky right-0",
      },
      cell: ({ row }) => (
        <div className="flex items-center justify-end space-x-4">
          {canAcceptRequest &&
            row.original.status_request === STATUS_APPROVED_ENUM.PENDING && (
              <div className="flex items-center gap-2">
                <Button
                  size="xs"
                  className="border-0"
                  color="primary"
                  onClick={() => {
                    if (!canAcceptRequest) return;
                    handleApproveReject({
                      id: row.original.suspect_id,
                      approve_or_reject: "approved",
                    });
                  }}
                >
                  Terima
                </Button>
                <Button
                  size="xs"
                  color="alternative"
                  onClick={() => {
                    if (!canAcceptRequest) return;
                    handleApproveReject({
                      id: row.original.suspect_id,
                      approve_or_reject: "rejected",
                    });
                  }}
                >
                  Tolak
                </Button>
              </div>
            )}
          {canEditAccess &&
            row.original.status_request === STATUS_APPROVED_ENUM.APPROVED && (
              <button
                onClick={() => {
                  handleClickEdit(
                    row.original.suspect_id,
                    row.original.status_request as STATUS_APPROVED_ENUM,
                  );
                }}
                className="text-neutral-14 hover:text-neutral-11 cursor-pointer"
              >
                <HiOutlinePencil className="h-5 w-5" />
              </button>
            )}
          <Link
            href={routes.managements.form.view(row.original?.suspect_id ?? 0)}
            className="text-neutral-14 hover:text-neutral-11"
          >
            <HiOutlineEye className="h-5 w-5" />
          </Link>
        </div>
      ),
    },
  ];

  return (
    <Table
      onPageIndexChange={props.onPageIndexChange}
      manualPagination={props.manualPagination}
      data={listFormRequestEdit?.data ?? []}
      columns={columns}
      pageCount={listFormRequestEdit?.total_page}
      isLoading={isLoadingListFormRequestEdit}
    />
  );
}
