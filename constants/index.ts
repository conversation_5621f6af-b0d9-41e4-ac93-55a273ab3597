export const APP_NAME = "Kejaksaan Republik Indonesia";

export const AVATAR_DEFAULT_URL = (name: string) =>
  `https://ui-avatars.com/api/?name=${name}&background=0f725e3c&color=0f725e`;

export enum ROLE_ENUM {
  ADMIN = "admin",
  INTEL = "intel",
  PIDUM = "pidum",
  PIDSUS = "pidsus",
  KEJARI = "kejari",
  KEJATI = "kejati",
  KEJAGUNG = "kejagung",
}

export const ROLE_LABEL: Record<string, string> = {
  [ROLE_ENUM.ADMIN]: "Admin",
  [ROLE_ENUM.INTEL]: "Intel",
  [ROLE_ENUM.PIDUM]: "Pidum",
  [ROLE_ENUM.PIDSUS]: "Pidsus",
  [ROLE_ENUM.KEJARI]: "<PERSON>jari",
  [ROLE_ENUM.KEJATI]: "<PERSON><PERSON><PERSON>",
  [ROLE_ENUM.KEJAGUNG]: "Kejaksaan Agung",
};

export const DROPDOWN_STATUS_PERKARA = [
  {
    label: "DPO",
    value: "dpo",
  },
  {
    label: "Tersangka",
    value: "tersangka",
  },
  {
    label: "Terdakwa",
    value: "terdakwa",
  },
  {
    label: "Terpidana",
    value: "terpidana",
  },
];

export const DROPDOWN_ASAL_PERKARA = [
  {
    label: "Polres",
    value: "polres",
  },
  {
    label: "Polri",
    value: "polri",
  },
  {
    label: "Polda",
    value: "polda",
  },
  {
    label: "Polsek",
    value: "polsek",
  },
  {
    label: "Bareskrim",
    value: "bareskrim",
  },
  {
    label: "Kejaksaan",
    value: "kejaksaan",
  },
  {
    label: "KPK",
    value: "kpk",
  },
];

export const DROPDOWN_JENIS_PERKARA = [
  {
    label: "Pidana Umum",
    value: "pidum",
  },
  {
    label: "Pidana Khusus",
    value: "pidsus",
  },
];

export const DROPDOWN_KEWARGANEGARAAN = [
  { value: "indonesian", label: "Warga Negara Indonesia" },
  { value: "foreigner", label: "Warga Negara Asing" },
];

export const DROPDOWN_JENIS_IDENTITAS = [
  {
    label: "Kartu Tanda Penduduk",
    value: "ktp",
  },
  // {
  //   label: "Surat Izin Mengemudi",
  //   value: "SIM",
  // },
  {
    label: "Paspor",
    value: "passport",
  },
];

export const DROPDOWN_YA_TIDAK = [
  { label: "Ya", value: "1" },
  { label: "Tidak", value: "0" },
];

export const DROPDOWN_GENDER = [
  { label: "Wanita", value: "female" },
  { label: "Pria", value: "male" },
];

export const GENDER_LABEL: Record<string, string> = {
  female: "Wanita",
  male: "Pria",
};

export const IS_RECIDIVIST: Record<string, string> = {
  true: "Ya",
  false: "Tidak",
};

export const CITIZENSHIP_LABEL: Record<string, string> = {
  indonesian: "Warga Negara Indonesia",
  foreigner: "Warga Negara Asing",
};

export enum STATUS_PERKARA_ENUM {
  TERSANGKA = "tersangka",
  TERDAKWA = "terdakwa",
  TERPIDANA = "terpidana",
  DPO = "dpo",
}

export const STATUS_PERKARA_LABEL: Record<string, string> = {
  [STATUS_PERKARA_ENUM.TERSANGKA]: "Tersangka",
  [STATUS_PERKARA_ENUM.TERDAKWA]: "Terdakwa",
  [STATUS_PERKARA_ENUM.TERPIDANA]: "Terpidana",
  [STATUS_PERKARA_ENUM.DPO]: "DPO",
};

export enum JENIS_PERKARA_ENUM {
  PIDUM = "pidum",
  PIDSUS = "pidsus",
}

export const JENIS_PERKARA_LABEL: Record<string, string> = {
  [JENIS_PERKARA_ENUM.PIDUM]: "Pidum",
  [JENIS_PERKARA_ENUM.PIDSUS]: "Pidus",
};

export enum ASAL_PERKARA_ENUM {
  POLRES = "polres",
  POLRI = "polri",
  POLDA = "polda",
  POLSEK = "polsek",
  BARESKRIM = "bareskrim",
  KEJAKSAAN = "kejaksaan",
  KPK = "kpk",
}

export const ASAL_PERKARA_LABEL: Record<string, string> = {
  [ASAL_PERKARA_ENUM.POLRES]: "Polres",
  [ASAL_PERKARA_ENUM.POLRI]: "Polri",
  [ASAL_PERKARA_ENUM.POLDA]: "Polda",
  [ASAL_PERKARA_ENUM.POLSEK]: "Polsek",
  [ASAL_PERKARA_ENUM.BARESKRIM]: "Bareskrim",
  [ASAL_PERKARA_ENUM.KEJAKSAAN]: "Kejaksaan",
  [ASAL_PERKARA_ENUM.KPK]: "KPK",
};

export enum STATUS_APPROVED_ENUM {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

export const STATUS_APPROVED_LABEL: Record<string, string> = {
  [STATUS_APPROVED_ENUM.PENDING]: "Pending",
  [STATUS_APPROVED_ENUM.APPROVED]: "Approved",
  [STATUS_APPROVED_ENUM.REJECTED]: "Rejected",
};
