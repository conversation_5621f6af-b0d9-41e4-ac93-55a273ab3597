import { Badge, Dropdown, DropdownTheme } from "flowbite-react";
import { NotificationItemApproval } from "../../../ui/notification/notification-item";
import { ReactElement } from "react";
import { NotificationData } from "@/services/notification/notification-get.interface";
import { BiBell } from "react-icons/bi";
import { SkeletonUserProfile } from "@/components/ui/skeletons";
import { usePopupStore } from "@/stores/popup/popup";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useLoadingOverlayStore } from "@/stores/loading-overlay/loading-overlay";
import { ErrorResponse } from "@/lib/error-handler/error-handler.interface";
import { Icon } from "../../icon";
import { requestMarkAllAsRead } from "@/services/notification/notification-post";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";
import { useApproveRejectRequestForm } from "@/lib/hooks/use-approve-reject-request-form";

interface NotificationDropdownProps {
  label?: string;
  renderTrigger: (theme: DropdownTheme) => ReactElement;
  totalUnread?: number;
  data: NotificationData[];
  isLoading?: boolean;
  canAcceptRequest?: boolean;
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

export const NotificationDropdown = ({
  label = "",
  renderTrigger,
  totalUnread = 0,
  data,
  isLoading = false,
  canAcceptRequest = false,
  onScroll,
}: NotificationDropdownProps) => {
  const { openPopup, closePopup } = usePopupStore();
  const { hide, show } = useLoadingOverlayStore();
  const queryClient = useQueryClient();

  const { handleApproveReject } = useApproveRejectRequestForm();

  const submitMarkAll = useMutation({
    mutationFn: () => requestMarkAllAsRead(),
    onSuccess: () => {
      hide();
      queryClient.invalidateQueries({ queryKey: ["listNotification"] });
      openPopup({
        title: "Berhasil Menandai Semua Notifikasi",
        message: "Semua notifikasi telah ditandai sebagai dibaca",
        classNameContainerActions: "justify-end",
        icon: <Icon type="success" />,
        withHeader: false,
        size: "xl",
        actions: [
          {
            label: "Tutup",
            type: "button",
            onClick: () => {
              closePopup();
            },
          },
        ],
      });
    },
    onError: (error: ErrorResponse) => {
      hide();
      openPopup({
        title: "Gagal Menyimpan Data",
        message: `Terjadi kesalahan saat menyimpan data, silahkan coba lagi. ${error.message}`,
        classNameContainerActions: "justify-end",
        icon: <Icon type="error" />,
        size: "xl",
        actions: [
          {
            label: "Kembali",
            type: "button",
            onClick: () => closePopup(),
          },
        ],
      });
    },
  });

  const handleMarkAllAsRead = () => {
    show("Menandai semua sebagai telah dibaca...");
    submitMarkAll.mutateAsync();
  };

  return (
    <Dropdown
      label={label}
      dismissOnClick={false}
      renderTrigger={renderTrigger}
      className="w-full max-w-[400px] min-w-[300px] rounded-lg border border-gray-300 shadow-lg"
    >
      <div className="flex items-center justify-between border-b border-gray-300 bg-white p-4 shadow-md">
        <div className="text-dark flex items-center gap-2 text-base font-semibold">
          Notifikasi{" "}
          {totalUnread > 0 && (
            <Badge className="bg-primary-500 rounded-full text-white">
              {totalUnread}
            </Badge>
          )}
        </div>
        <div>
          {totalUnread > 0 && (
            <div
              onClick={handleMarkAllAsRead}
              className={twMerge(
                "text-primary-500 cursor-pointer text-xs font-medium hover:underline",
                isLoading ? "cursor-not-allowed opacity-50" : "",
              )}
            >
              Tandai semua telah dibaca
            </div>
          )}
        </div>
      </div>

      <div className="max-h-[500px] overflow-y-auto" onScroll={onScroll}>
        {isLoading ? (
          Array.from({ length: 3 }).map((_, idx) => (
            <SkeletonUserProfile
              className="border-b border-gray-100"
              key={idx}
            />
          ))
        ) : data.length === 0 ? (
          <div
            onClick={handleMarkAllAsRead}
            className="flex flex-col items-center justify-center space-y-2 py-10 text-gray-400"
          >
            <BiBell className="h-10 w-10" />
            <div className="text-sm">Tidak ada notifikasi</div>
          </div>
        ) : (
          data.map((item, index) => (
            <NotificationItemApproval
              key={index}
              data={item}
              canAcceptRequest={canAcceptRequest}
              onAccept={() => {
                if (!canAcceptRequest) return;
                handleApproveReject({
                  id: item.id,
                  approve_or_reject: "approved",
                });
              }}
              onReject={() => {
                if (!canAcceptRequest) return;
                handleApproveReject({
                  id: item.id,
                  approve_or_reject: "rejected",
                });
              }}
            />
          ))
        )}
      </div>

      {/* <div className="flex items-center justify-center border-t border-gray-200 bg-white px-4 py-3">
        <div className="text-primary-500 cursor-pointer text-sm hover:underline">
          Lihat semua notifikasi
        </div>
      </div> */}
    </Dropdown>
  );
};
