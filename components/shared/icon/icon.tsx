import React from "react";
import { SuccessIcon } from "./success-icon";
import { FailedIcon } from "./failed-icon";
import { WarningIcon } from "./warning-icon";
import { InfoIcon } from "./info-icon";

interface IconProps {
  type: "success" | "error" | "warning" | "info";
}

export const Icon: React.FC<IconProps> = ({ type }) => {
  switch (type) {
    case "success":
      return <SuccessIcon />;
    case "error":
      return <FailedIcon />;
    case "warning":
      return <WarningIcon />;
    case "info":
      return <InfoIcon />;
    default:
      return <InfoIcon />;
  }
};
