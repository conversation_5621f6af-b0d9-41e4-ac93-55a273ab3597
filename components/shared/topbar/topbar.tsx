"use client";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "flowbite-react";
import Logo from "@/assets/logo.png";
import Image from "next/image";
import { routes } from "@/lib/routes";
import { useSession } from "next-auth/react";
import { APP_NAME } from "@/constants";
import { FiSettings } from "react-icons/fi";
import { NotificationDropdown } from "../notification";
import { BiBell } from "react-icons/bi";
import Link from "next/link";
import { UserInfoTopbar } from "./user-info-topbar";
import { useQuery } from "@tanstack/react-query";
import { getListNotification } from "@/services/notification/notification-get";
import { signOut } from "@/utils/auth";

export const TopBar = () => {
  const { data: dataSession } = useSession();
  const canAcceptRequest =
    dataSession?.user?.user_access?.can_accept_request ?? false;
  const handleLogout = async () => {
    await signOut();
  };

  const {
    data: listNotification,
    isLoading: isLoadingListNotification,
    // isError: isErrorListNotification,
  } = useQuery({
    queryKey: ["listNotification"],
    queryFn: () => getListNotification(),
  });

  return (
    <div className="fixed top-0 z-50 w-full bg-white py-4 shadow-[0_0_8px_rgba(70,52,52,0.15)]">
      <div className="container-fluid mx-auto flex flex-wrap justify-between">
        <Link href={routes.dashboard} className="hidden sm:block">
          <div className="flex items-center">
            <Image src={Logo} height={50} width={50} alt="Logo Kejaksaan" />
            <span className="text-primary-500 ms-2 self-center text-lg font-semibold whitespace-nowrap">
              {APP_NAME}
            </span>
          </div>
        </Link>
        <div className="flex flex-wrap items-center gap-2">
          <Button
            type="button"
            color="alternative"
            className="h-10 w-10 cursor-pointer rounded-full border-0 p-2"
          >
            <FiSettings size={21} />
          </Button>
          <NotificationDropdown
            totalUnread={listNotification?.total_unread ?? 0}
            data={listNotification?.data ?? []}
            isLoading={isLoadingListNotification}
            canAcceptRequest={canAcceptRequest}
            renderTrigger={() => {
              return (
                <div className="relative">
                  <Button
                    type="button"
                    color="alternative"
                    className="relative h-10 w-10 cursor-pointer rounded-full border-0 p-2"
                  >
                    {(listNotification?.total_unread ?? 0) > 0 && (
                      <Badge className="bg-primary-500 absolute top-0 right-0 rounded-full text-[10px] text-white">
                        {listNotification?.total_unread ?? 0}
                      </Badge>
                    )}
                    <BiBell size={22} />
                  </Button>
                </div>
              );
            }}
          />
          <UserInfoTopbar
            name={dataSession?.user?.full_name ?? ""}
            email={dataSession?.user?.email ?? ""}
            handleLogout={handleLogout}
          />
        </div>
      </div>
    </div>
  );
};
