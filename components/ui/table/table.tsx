"use client";

import {
  Table as FBTable,
  TableBody,
  TableCell,
  TableHead,
  TableHeadCell,
  TableRow,
} from "flowbite-react";
import {
  flexRender,
  getCoreRowModel,
  PaginationState,
  useReactTable,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
import { Pagination } from "../pagination";
import { TableProps } from "./table.type";

const Table = <D,>(props: TableProps<D>) => {
  const {
    total_data,
    data,
    columns,
    manualPagination = false,
    pageCount,
    isLoading = false,
    onPageIndexChange,
  } = props;

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const table = useReactTable({
    data,
    columns,
    rowCount: data.length,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination,
    pageCount: pageCount ?? Math.ceil(data.length / pagination.pageSize),
  });

  const rows = table.getRowModel().rows;

  const renderSkeleton = () => {
    return [...Array(6)].map((_, rowIndex) => (
      <TableRow key={`skeleton-${rowIndex}`}>
        {columns.map((_, colIndex) => (
          <TableCell key={`skeleton-${rowIndex}-${colIndex}`}>
            <div className="h-8 w-full animate-pulse rounded bg-gray-200" />
          </TableCell>
        ))}
      </TableRow>
    ));
  };

  const renderNoData = () => (
    <TableRow>
      <TableCell colSpan={columns.length} className="text-center text-gray-500">
        Tidak ada data ditemukan
      </TableCell>
    </TableRow>
  );
  useEffect(() => {
    if (onPageIndexChange) {
      onPageIndexChange(pagination.pageIndex + 1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.pageIndex]);

  useEffect(() => {
    if (manualPagination) {
      setPagination((prev) => ({
        ...prev,
        pageIndex: 0,
      }));
    }
  }, [manualPagination]);
  return (
    <div
      style={{ boxShadow: "0px 2px 4px -2px #1018280F" }}
      className="overflow-x-auto rounded-lg border border-gray-200 bg-white"
    >
      <FBTable>
        <TableHead>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHeadCell
                  key={header.id}
                  className={header.column.columnDef.meta?.classNameHead ?? ""}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </TableHeadCell>
              ))}
            </TableRow>
          ))}
        </TableHead>

        <TableBody>
          {isLoading
            ? renderSkeleton()
            : rows.length === 0
              ? renderNoData()
              : rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className={cell.column.columnDef.meta?.className ?? ""}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
        </TableBody>
      </FBTable>

      <div className="flex flex-row items-center justify-between px-6 py-3 text-sm text-gray-600">
        <span>
          Menampilkan{" "}
          {isLoading || rows.length === 0
            ? 0
            : pagination.pageIndex * pagination.pageSize + 1}{" "}
          sampai{" "}
          {isLoading || rows.length === 0
            ? 0
            : Math.min(
                (pagination.pageIndex + 1) * pagination.pageSize,
                total_data ?? rows.length,
              )}{" "}
          dari {total_data} Data
        </span>

        <div className="flex overflow-x-auto sm:justify-center">
          <Pagination
            currentPage={table.getState().pagination.pageIndex + 1}
            totalPages={Math.max(
              1,
              isLoading ? 1 : (pageCount ?? table.getPageCount() ?? 1),
            )}
            onPageChange={(val) => table.setPageIndex(val - 1)}
            showIcons
          />
        </div>
      </div>
    </div>
  );
};

Table.displayName = "Table";

export default Table;
