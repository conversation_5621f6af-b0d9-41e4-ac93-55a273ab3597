import { ColumnDef } from "@tanstack/react-table";

// Extend the ColumnMeta interface to include className
declare module "@tanstack/react-table" {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData, TValue> {
    className?: string;
    classNameHead?: string;
  }
}

export type TableProps<D> = {
  total_data?: number;
  data: D[];
  columns: ColumnDef<D>[];
  pageCount?: number;
  isLoading?: boolean;
  manualPagination?: boolean;
  onPageIndexChange?: (pageIndex: number) => void;
};
