import { <PERSON><PERSON>, But<PERSON> } from "flowbite-react";
import { NotificationItemApprovalProps } from "./notification-types";
import { NotificationStatusApproval } from "@/services/notification/notification-get.interface";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";
import { AVATAR_DEFAULT_URL, STATUS_APPROVED_ENUM } from "@/constants";
import { FiClock } from "react-icons/fi";
import { BiFileBlank } from "react-icons/bi";
import { useRouter } from "next/navigation";
import { routes } from "@/lib/routes";

export const NotificationItemApproval = ({
  data,
  onAccept,
  onReject,
  canAcceptRequest,
}: NotificationItemApprovalProps) => {
  const router = useRouter();
  return (
    <div
      className={twMerge(
        "flex items-start gap-2 border-b border-gray-300 p-4 hover:bg-gray-50",
        !data.is_read && "bg-gray-100 hover:bg-gray-200",
      )}
    >
      <Avatar
        alt="User"
        img={AVATAR_DEFAULT_URL(data.requested_by ?? "")}
        rounded
        className="!h-10 !w-16"
      />
      <div className="space-y-3">
        <div>
          <div className="text-sm font-semibold text-gray-900">
            {data.requested_by ?? ""}
          </div>
          <div className="text-xs text-gray-600">
            Request untuk edit form berikut:
          </div>
          {/* <div className="border-main-1 mt-3 rounded-lg border bg-white px-3 py-2 text-sm hover:cursor-pointer hover:bg-gray-50">
            <Link href={`/managements/form/${data.suspect_id}`} className="">
              Kartu TIK - {data.name_tersangka}
            </Link>
          </div> */}
        </div>
        <Button
          color="alternative"
          size="sm"
          onClick={() => {
            router.push(routes.managements.form.view(data.suspect_id));
          }}
          className="border-primary-500 bordertext-left truncate bg-white text-xs shadow-[0_0_4px_rgba(70,52,52,0.15)]"
        >
          <BiFileBlank size={16} className="text-primary-500 mr-2" />
          Kartu TIK - [{data.name_tersangka}]
        </Button>
        <div>
          <div className="text-xs font-medium text-gray-600">Alasan:</div>
          <div className="text-xs font-semibold text-gray-900">
            {data.note || "-"}
          </div>
        </div>
        {data.status === STATUS_APPROVED_ENUM.PENDING && canAcceptRequest ? (
          <div className="flex items-center gap-2">
            <Button
              size="xs"
              className="border-0"
              color="primary"
              onClick={onAccept}
            >
              Terima
            </Button>
            <Button size="xs" color="alternative" onClick={onReject}>
              Tolak
            </Button>
          </div>
        ) : (
          <NotificationStatusBadge status={data.status} />
        )}
        <div className="text-xs font-medium text-gray-600">
          <FiClock className="mr-1 inline align-middle" />
          {data.received_ago || "-"}
        </div>
      </div>
    </div>
  );
};

export const NotificationStatusBadge = ({
  status,
}: {
  status: NotificationStatusApproval;
}) => {
  const statusClasses: Record<NotificationStatusApproval, string> = {
    pending: "bg-blue-100 border border-blue-600 text-blue-600",
    rejected: "bg-red-100 border border-red-600 text-red-600",
    approved: "bg-green-100 border border-green-600 text-green-600",
  };
  const statusText: Record<NotificationStatusApproval, string> = {
    pending: "menunggu",
    rejected: "ditolak",
    approved: "diberikan",
  };
  return (
    <div
      className={`inline-block rounded-lg px-3 py-2 text-xs font-semibold ${statusClasses[status] ?? "bg-gray-100 text-gray-800"}`}
    >
      Akses edit {statusText[status] ?? "Tidak Diketahui"}
    </div>
  );
};
