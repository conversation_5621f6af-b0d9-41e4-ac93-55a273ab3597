/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { FC } from "react";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

import clsx from "clsx";
import { Datepicker, Label, Select, Textarea, TextInput } from "flowbite-react";
import { Controller, useFormContext } from "react-hook-form";

import { FormInputProps } from "./form-input-types";
import { PhoneInput } from "../phone-input";
import { RadioInput } from "../radio-input";
import { Dropzone } from "../dropzone";
import { SignatureInput } from "../signature-input";
import { ReactSelect } from "../react-select/react-select";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";

dayjs.extend(customParseFormat);

export const FormInput: FC<FormInputProps> = ({
  id,
  name,
  label,
  withRequiredStar = false,
  labelClassName = "",
  type = "text",
  inputType = "text",
  placeholder,
  required = false,
  sizing = "md",
  options = [],
  onChange,
  className = "",
  leftElement,
  rightElement,
  isInvalid = false,
  isLoading = false,
  disabled = false,
  errorMessage = "",
  maxCharacters,
  maxDate,
  minDate,
  dateFormat,
  ...restProps
}) => {
  const { control } = useFormContext();

  const sharedProps = {
    id,
    required,
    disabled,
    className: clsx("min-w-0 flex-1 mb-1", className),
    sizing,
    ...(isInvalid && { color: "failure" }),
    ...restProps,
  };

  const handleChange = (
    fieldOnChange: (...args: any[]) => void,
    value: any,
  ) => {
    if (inputType === "react-select") {
      if (restProps.reactSelectProps?.isMulti && Array.isArray(value)) {
        value = value.map((v) => (typeof v === "object" ? v.value : v));
      } else if (typeof value === "object" && value !== null) {
        value = value.value;
      }
    }

    if (inputType === "date") {
      value = value ? dayjs(value).format(dateFormat || "DD/MM/YYYY") : "";
    }
    fieldOnChange(value ?? "");
    onChange?.(value ?? "");
  };

  const renderField = (field: any) => {
    const value = field.value;

    switch (inputType) {
      case "select":
        return (
          <Select
            {...sharedProps}
            {...field}
            onChange={(e) => handleChange(field.onChange, e.target.value)}
          >
            {options.map((opt) => (
              <option key={opt.value} value={opt.value}>
                {opt.label}
              </option>
            ))}
          </Select>
        );

      case "react-select":
        return (
          <ReactSelect
            isLoading={isLoading}
            isDisabled={disabled}
            placeholder={placeholder}
            {...sharedProps.reactSelectProps}
            {...field}
            value={
              restProps.reactSelectProps?.isMulti
                ? options.filter((opt) => value?.includes(opt.value))
                : options.find((opt) => opt.value === value) || null
            }
            customStyles={{
              control: () => ({
                minHeight: "44px",
                marginBottom: 4,
                ...(isInvalid && { borderColor: "red" }),
              }),
            }}
            options={options}
            onChange={(option) => handleChange(field.onChange, option)}
          />
        );

      case "textarea":
        return (
          <Textarea
            {...sharedProps}
            placeholder={placeholder}
            value={value}
            onChange={(e) => handleChange(field.onChange, e)}
            onBlur={field.onBlur}
          />
        );

      case "date":
        const parsedDate = value
          ? dayjs(value, dateFormat || "DD/MM/YYYY").isValid()
            ? dayjs(value, dateFormat || "DD/MM/YYYY").toDate()
            : null
          : null;

        return (
          <>
            {dateFormat && (
              <div className="flex justify-end">
                <p className="text-neutral-14 text-[10px]">{dateFormat}</p>
              </div>
            )}
            <Datepicker
              {...sharedProps}
              placeholder={placeholder}
              value={parsedDate}
              onChange={(e) => {
                handleChange(field.onChange, e ? dayjs(e).toDate() : "");
              }}
              onBlur={field.onBlur}
              maxDate={maxDate}
              minDate={minDate}
              language="id-ID"
              labelTodayButton="Hari Ini"
              labelClearButton="Reset"
              theme={{
                root: {
                  input: {
                    field: {
                      icon: {
                        base: twMerge("!pl-3"),
                      },
                    },
                  },
                },
              }}
            />
          </>
        );

      case "phone":
        return (
          <PhoneInput
            {...sharedProps}
            placeholder={placeholder}
            value={value}
            onChange={(e) => handleChange(field.onChange, e)}
            isInvalid={isInvalid}
          />
        );

      case "radio":
        return (
          <RadioInput
            {...sharedProps}
            value={value}
            onChange={(e) => handleChange(field.onChange, e)}
            isInvalid={isInvalid}
          />
        );

      case "dropzone":
        return (
          <Dropzone
            {...sharedProps}
            value={value}
            label={label}
            onChange={(e) => handleChange(field.onChange, e)}
            isInvalid={isInvalid}
          />
        );

      case "signature":
        return (
          <SignatureInput
            {...sharedProps}
            value={value}
            onChange={(e) => handleChange(field.onChange, e)}
            isInvalid={isInvalid}
          />
        );

      default:
        return (
          <>
            {maxCharacters && (
              <div className="flex justify-end">
                <p
                  className={`text-[10px] ${
                    isInvalid ? "text-red-600" : "text-neutral-14"
                  }`}
                >
                  {value?.length || 0}/{maxCharacters} Karakter
                </p>
              </div>
            )}
            <TextInput
              {...sharedProps}
              type={type}
              placeholder={placeholder}
              value={value}
              onChange={(e) => handleChange(field.onChange, e)}
              onBlur={field.onBlur}
              {...(leftElement && { icon: () => leftElement })}
              {...(rightElement && { rightIcon: () => rightElement })}
            />
          </>
        );
    }
  };

  return (
    <div className={clsx("relative flex w-full flex-col space-y-4", className)}>
      {label && (
        <Label
          htmlFor={id}
          className={clsx(
            labelClassName,
            "font-medium",
            isInvalid && "text-red-600",
          )}
        >
          {label}
          {withRequiredStar && (
            <span className="text-base text-red-500">*</span>
          )}
        </Label>
      )}

      <Controller
        name={name}
        control={control}
        render={({ field }) => renderField(field)}
      />

      {errorMessage && (
        <div className="text-xs font-medium text-red-600">{errorMessage}</div>
      )}
    </div>
  );
};
