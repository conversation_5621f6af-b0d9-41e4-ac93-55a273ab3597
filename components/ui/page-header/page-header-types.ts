export interface ActionButton {
  icon?: React.ReactNode;
  label?: string;
  variant?: "primary" | "secondary" | "outline" | "text" | "gradient";
  onClick?: () => void;
  href?: string;
  element?: React.ReactNode;
  isButton?: boolean;
  disabled?: boolean;
}

export interface PageHeaderProps {
  className?: string;
  title: string;
  backHref?: string;
  backLabel?: string;
  backIcon?: React.ReactNode;
  rightElement?: ActionButton[];
  leftElement?: ActionButton[];
}
