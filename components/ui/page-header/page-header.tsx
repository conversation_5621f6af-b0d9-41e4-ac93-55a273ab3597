"use client";
import React, { Fragment } from "react";
import { Button } from "@/components/ui/button";
import { PageHeaderProps } from "./page-header-types";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";

export const PageHeader: React.FC<PageHeaderProps> = ({
  className,
  title,
  backHref,
  backLabel = "",
  backIcon,
  rightElement = [],
  leftElement = [],
}) => {
  const hasBackButton = !!backHref;

  return (
    <div
      className={twMerge(
        "flex flex-wrap items-center justify-between gap-4",
        className,
      )}
    >
      <div className="flex items-center gap-2">
        {leftElement.length > 0 ? (
          leftElement?.map((action, index) => (
            <Fragment key={index}>
              {action.isButton ? (
                <Button
                  size="sm"
                  leftIcon={action.icon}
                  variant={action.variant}
                >
                  {action.label}
                </Button>
              ) : (
                action.element
              )}
            </Fragment>
          ))
        ) : (
          <Fragment>
            {hasBackButton && (
              <Button
                size="sm"
                leftIcon={backIcon}
                href={backHref}
                variant="text"
              >
                {backLabel}
              </Button>
            )}
            <h1 className="text-2xl font-medium">{title}</h1>
          </Fragment>
        )}
      </div>

      <div className="flex flex-wrap gap-2">
        {rightElement.map((action, index) => (
          <Fragment key={index}>
            {action.isButton ? (
              <Button
                onClick={action.onClick}
                size="sm"
                leftIcon={action.icon}
                href={action.href}
                variant={action.variant}
                disabled={action.disabled}
              >
                {action.label}
              </Button>
            ) : (
              action.element
            )}
          </Fragment>
        ))}
      </div>
    </div>
  );
};
