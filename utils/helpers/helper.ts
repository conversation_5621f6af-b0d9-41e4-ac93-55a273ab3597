export function extractParamsFromPattern(
  pattern: string,
  actualPath: string,
): Record<string, string> | null {
  const patternSegments = pattern.split("/").filter(Boolean);
  const pathSegments = actualPath.split("/").filter(Boolean);

  if (patternSegments.length !== pathSegments.length) return null;

  const params: Record<string, string> = {};

  for (let i = 0; i < patternSegments.length; i++) {
    const patternSegment = patternSegments[i];
    const pathSegment = pathSegments[i];

    const match = patternSegment.match(/^{(.+)}$/); // match {param}

    if (match) {
      const paramName = match[1];
      params[paramName] = pathSegment;
    } else if (patternSegment !== pathSegment) {
      // Static segment mismatch
      return null;
    }
  }

  return params;
}

export function isPathMatchingPattern(
  pattern: string,
  pathname: string,
): boolean {
  const patternSegments = pattern.split("/").filter(Boolean);
  const pathSegments = pathname.split("/").filter(Boolean);

  if (patternSegments.length !== pathSegments.length) return false;

  for (let i = 0; i < patternSegments.length; i++) {
    const patternSegment = patternSegments[i];
    const pathSegment = pathSegments[i];

    const isParam = patternSegment.match(/^{[^}]+}$/);
    if (!isParam && patternSegment !== pathSegment) {
      return false;
    }
  }

  return true;
}
