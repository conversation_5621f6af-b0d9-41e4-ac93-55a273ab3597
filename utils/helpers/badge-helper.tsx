import {
  JENIS_PERKARA_ENUM,
  STATUS_APPROVED_ENUM,
  STATUS_PERKARA_ENUM,
} from "@/constants";
import { Badge } from "flowbite-react";

type StatusPerkaraType = `${STATUS_PERKARA_ENUM}`;

export const badgeStatusPerkara = (status: StatusPerkaraType) => {
  let badgeColor: "failure" | "pink" | "warning";

  switch (status) {
    case STATUS_PERKARA_ENUM.DPO:
      badgeColor = "failure";
      break;
    case STATUS_PERKARA_ENUM.TERDAKWA:
      badgeColor = "pink";
      break;
    case STATUS_PERKARA_ENUM.TERPIDANA:
      badgeColor = "failure";
      break;
    default:
      badgeColor = "warning";
      break;
  }

  return (
    <Badge
      color={badgeColor}
      className="mx-auto w-fit rounded-2xl px-4 py-1.5 capitalize"
    >
      {status}
    </Badge>
  );
};

export const badgeJenisPerkara = (jenis: string) => {
  let badgeColor: "info" | "success" | "warning";
  switch (jenis) {
    case JENIS_PERKARA_ENUM.PIDUM:
      badgeColor = "info";
      break;
    case JENIS_PERKARA_ENUM.PIDSUS:
      badgeColor = "success";
      break;
    default:
      badgeColor = "warning";
      break;
  }

  return (
    <Badge
      color={badgeColor}
      className="mx-auto w-fit rounded-2xl px-4 py-1.5 capitalize"
    >
      {jenis}
    </Badge>
  );
};

type StatusRequestEditFormType = `${STATUS_APPROVED_ENUM}`;
export const badgeStatusRequestEditForm = (
  status: StatusRequestEditFormType,
) => {
  let badgeColor: "info" | "success" | "warning" | "failure";
  switch (status) {
    case STATUS_APPROVED_ENUM.PENDING:
      badgeColor = "info";
      break;
    case STATUS_APPROVED_ENUM.APPROVED:
      badgeColor = "success";
      break;
    case STATUS_APPROVED_ENUM.REJECTED:
      badgeColor = "failure";
      break;
    default:
      badgeColor = "info";
      break;
  }

  return (
    <Badge
      color={badgeColor}
      className="mx-auto w-fit rounded-2xl px-4 py-1.5 capitalize"
    >
      {status}
    </Badge>
  );
};
