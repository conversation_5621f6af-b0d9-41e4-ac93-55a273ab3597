/* eslint-disable @typescript-eslint/no-explicit-any */
import { debounce } from "lodash";
import { useMutation } from "@tanstack/react-query";
import { checkLetterCode } from "@/services/form/form-post";
import { CheckLetterCodeResponse } from "@/services/form/form-post.interface";

interface LetterCodeValidationCallbacks {
  setError: (error: string) => void;
  setIsChecking: (isChecking: boolean) => void;
}

export const useCheckLetterCodeMutation = (
  callbacks: LetterCodeValidationCallbacks,
) => {
  return useMutation({
    mutationFn: (letterCode: string) =>
      checkLetterCode({ letter_code: letterCode }),
    onSuccess: (response: CheckLetterCodeResponse) => {
      if (response?.exist) {
        callbacks.setError(
          "Nomor surat sudah pernah diinput, silahkan coba dengan nomor lain",
        );
      } else {
        callbacks.setError("");
      }
    },
    onError: (error: any) => {
      if (error?.response?.data?.data?.exist) {
        callbacks.setError(
          "Nomor surat sudah pernah diinput, silahkan coba dengan nomor lain",
        );
      } else {
        callbacks.setError("Gagal memvalidasi nomor surat");
      }
    },
    onSettled: () => {
      callbacks.setIsChecking(false);
    },
  });
};

export const createDebouncedCheckLetterCode = (
  mutate: (letterCode: string) => void,
  callbacks: LetterCodeValidationCallbacks,
) => {
  return debounce((letterCode: string) => {
    if (!letterCode || letterCode.trim() === "") {
      callbacks.setError("");
      callbacks.setIsChecking(false);
      return;
    }

    callbacks.setIsChecking(true);
    mutate(letterCode);
  }, 1000);
};
