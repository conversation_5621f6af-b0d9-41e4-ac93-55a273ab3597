/* eslint-disable @typescript-eslint/no-explicit-any */
import { debounce } from "lodash";
import { useMutation } from "@tanstack/react-query";
import { checkRefNumber } from "@/services/form/form-post";
import { CheckRefNumberResponse } from "@/services/form/form-post.interface";

interface RefNumberValidationCallbacks {
  setError: (error: string) => void;
  setIsChecking: (isChecking: boolean) => void;
}

export const useCheckRefNumberMutation = (
  callbacks: RefNumberValidationCallbacks,
) => {
  return useMutation({
    mutationFn: (refNumber: string) =>
      checkRefNumber({ ref_number: refNumber }),
    onSuccess: (response: CheckRefNumberResponse) => {
      if (response.exist) {
        callbacks.setError(
          "Nomor referensi sudah pernah diinput, silahkan coba dengan nomor lain",
        );
      } else {
        callbacks.setError("");
      }
    },
    onError: (error: any) => {
      if (error?.response?.data?.data?.exist) {
        callbacks.setError(
          "Nomor referensi sudah pernah diinput, silahkan coba dengan nomor lain",
        );
      } else {
        callbacks.setError("Gagal memvalidasi nomor referensi");
      }
    },
    onSettled: () => {
      callbacks.setIsChecking(false);
    },
  });
};

export const createDebouncedCheckRefNumber = (
  mutate: (refNumber: string) => void,
  callbacks: RefNumberValidationCallbacks,
) => {
  return debounce((refNumber: string) => {
    if (!refNumber || refNumber.trim() === "") {
      callbacks.setError("");
      callbacks.setIsChecking(false);
      return;
    }

    callbacks.setIsChecking(true);
    mutate(refNumber);
  }, 1000);
};
