export const tableTheme = {
  table: {
    root: {
      base: "w-full text-left text-sm text-gray-700 ",
      shadow:
        "absolute left-0 top-0 -z-10 h-full w-full rounded-lg bg-white drop-shadow-md dark:bg-black",
      wrapper: "relative",
    },
    head: {
      base: "group/head text-xs uppercase text-gray-500 ",
      cell: {
        base: "bg-neutral-17 px-7 py-4 border-b border-gray-300",
        th: "border-b border-gray-300",
      },
    },
    body: {
      base: "group/body bg-white shadow-sm",
      cell: {
        base: "px-6 py-4 border-b border-gray-200  max-w-0 overflow-hidden text-ellipsis whitespace-nowrap",
        td: "border-b border-gray-20  max-w-0 overflow-hidden text-ellipsis whitespace-nowrap",
      },
    },
    row: {
      base: "group/row",
      hovered: "hover:bg-gray-50 dark:hover:bg-gray-600",
      striped:
        "odd:bg-white even:bg-gray-50 odd:dark:bg-gray-800 even:dark:bg-gray-700",
    },
  },
};

export type TableTheme = typeof tableTheme;
